















import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from DB.setup_db import session
from DB.models import WindTurbineData, SolarInverterData, DgrBothDb, SolarReport, WindReport, PlantAlarm
from helper.utils import get_hybrid_info_for_plant_id
from config.settings import Config
import csv
from typing import Optional, Set, <PERSON><PERSON>
import os
import json
from helper.utils import (
    generate_solar_dgr_pdf,
    generate_combined_wind_pdf,
    generate_combined_both_pdf,
    get_capacity_from_csv
)
from helper.logger_setup import setup_logger

# === Constants ===
WIND_SHEET = "wind"
SOLAR_SHEET = "solar"

WIND_EDIT_MAP = {
    "Edit Generation": "edit_generation",
    "Edit AVG. Wind Speed": "edit_avg_wind_speed",
    "Edit Generation Monthly": "edit_generation_monthly",
    "Edit AVG. Wind Speed Monthly": "edit_avg_wind_speed_monthly",
    "Edit Action": "edit_action",
    "Reason": "reason_edit"
}

SOLAR_EDIT_MAP = {
    "Edit Generation": "edit_generation",
    "Edit PR": "edit_pr",
    "Edit POA": "edit_poa",
    "Edit Generation Monthly": "edit_generation_monthly",
    "Edit PR Monthly": "edit_pr_monthly",
    "Edit POA Monthly": "edit_poa_monthly",
    "Edit Action": "edit_action",
    "Reason": "reason_edit"
}

# === Logging setup ===
logger = setup_logger(__name__, "import_turbine_inverter_data.log")

# --- Session utility ---
def _get_session(db_session=None):
    if db_session is not None:
        return db_session, False
    if callable(session):
        s = session()
        logger.debug("Created new DB session via session() factory.")
        return s, True
    return session, False

# --- CSV utility ---
def get_plant_metadata_from_csv(plant_id: str, csv_path: str):
    try:
        with open(csv_path, encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if row.get('Plant id', '').strip() == plant_id:
                    return {
                        "plant_long_name": row.get('Plant Name', '').strip(),
                        "plant_short_name": row.get('Plant id', '').strip(),
                        "customer_name": row.get('Customer Name', '').strip()
                    }
    except Exception:
        logger.exception("Error reading CSV: %s", csv_path)
    return {"plant_long_name": "", "plant_short_name": plant_id, "customer_name": ""}

# --- Aggregation ---
def aggregate_base_fields(df: pd.DataFrame, plant_id: str, date_obj: datetime.date, is_solar: bool=True):
    if df is None or df.empty:
        return {}
    try:
        filtered = df[(df["Plant ID"] == plant_id) & (pd.to_datetime(df["Date"]).dt.date == date_obj)]
        if filtered.empty:
            return {}
        if is_solar:
            return {
                "generation": filtered.get("Prescinto Generation", pd.Series([0])).sum(),
                "pr": filtered.get("Prescinto  PR", pd.Series([0])).mean(),
                "poa": filtered.get("Prescinto POA", pd.Series([0])).mean(),
                "generation_monthly": filtered.get("Prescinto  Generation Monthly", pd.Series([0])).sum(),
                "pr_monthly": filtered.get("Prescinto  PR Monthly", pd.Series([0])).mean(),
                "poa_monthly": filtered.get("Prescinto  POA Monthly", pd.Series([0])).mean(),
            }
        else:
            return {
                "generation": filtered.get("Prescinto Generation", pd.Series([0])).sum(),
                "wind_speed": filtered.get("Prescinto  AVG. Wind Speed", pd.Series([0])).mean(),
                "generation_monthly": filtered.get("Prescinto Generation Monthly", pd.Series([0])).sum(),
                "wind_speed_monthly": filtered.get("Prescinto  AVG. Wind Speed Monthly", pd.Series([0])).mean(),
            }
    except Exception:
        logger.exception("Error during aggregation")
        return {}

# --- Allowed plant-date lookup ---
def get_allowed_plant_dates_from_status():
    """
    Returns set of (plant_id, date) tuples for all records, regardless of status.
    This allows updates to all plant/date pairs, even if status is 'Sent'.
    """
    allowed = set()
    try:
        s, created = _get_session()
        try:
            for rec in s.query(DgrBothDb):
                if rec.plant_short_name_solar:
                    allowed.add((rec.plant_short_name_solar, rec.date))
                if rec.plant_short_name_wind:
                    allowed.add((rec.plant_short_name_wind, rec.date))
            for rec in s.query(SolarReport):
                allowed.add((rec.plant_short_name, rec.date))
            for rec in s.query(WindReport):
                allowed.add((rec.plant_short_name, rec.date))
        finally:
            if created:
                s.close()
    except Exception:
        logger.exception("Error reading allowed plant dates")
    return allowed

# --- Update functions for turbine/inverter ---
def update_wind_from_excel(df: pd.DataFrame, db_session=None, allowed_plant_dates: Optional[Set[Tuple[str, datetime.date]]]=None):
    s, created = _get_session(db_session)
    updated = 0
    try:
        if df is None or df.empty:
            return 0
        for idx, row in df.iterrows():
            try:
                if str(row.get("Edit Action", "")).strip() != "Updated":
                    continue
                date_obj = pd.to_datetime(row["Date"]).date()
                plant_id = str(row.get("Plant ID", "")).strip()
                turbine_name = str(row.get("Turbine Name", "")).strip()
                if allowed_plant_dates and (plant_id, date_obj) not in allowed_plant_dates:
                    continue
                record = s.query(WindTurbineData).filter_by(date=date_obj, plant_id=plant_id, turbine_name=turbine_name).first()
                if not record:
                    continue
                for col, field in WIND_EDIT_MAP.items():
                    val = row.get(col)
                    if pd.notnull(val) and str(val).strip():
                        setattr(record, field, val)
                # Set status from Edit Action column, unless approved is True
                if not (hasattr(record, "approved") and record.approved is True):
                    record.status = row.get("Edit Action", "")
                record.updated_at = datetime.now()
                updated += 1
            except Exception:
                logger.exception("Error processing wind row %d", idx)
        if created:
            s.commit()
    finally:
        if created:
            s.close()
    return updated

def update_solar_from_excel(df: pd.DataFrame, db_session=None, allowed_plant_dates: Optional[Set[Tuple[str, datetime.date]]]=None):
    s, created = _get_session(db_session)
    updated = 0
    try:
        if df is None or df.empty:
            return 0
        for idx, row in df.iterrows():
            try:
                if str(row.get("Edit Action", "")).strip() != "Updated":
                    continue
                date_obj = pd.to_datetime(row["Date"]).date()
                plant_id = str(row.get("Plant ID", "")).strip()
                inverter_name = str(row.get("Inverter Name", "")).strip()
                if allowed_plant_dates and (plant_id, date_obj) not in allowed_plant_dates:
                    continue
                record = s.query(SolarInverterData).filter_by(date=date_obj, plant_id=plant_id, inverter_name=inverter_name).first()
                if not record:
                    continue
                for col, field in SOLAR_EDIT_MAP.items():
                    val = row.get(col)
                    if pd.notnull(val) and str(val).strip():
                        setattr(record, field, val)
                # Set status from Edit Action column, unless approved is True
                if not (hasattr(record, "approved") and record.approved is True):
                    record.status = row.get("Edit Action", "")
                record.updated_at = datetime.now()
                updated += 1
            except Exception:
                logger.exception("Error processing solar row %d", idx)
        if created:
            s.commit()
    finally:
        if created:
            s.close()
    return updated

# --- Plant-level updates ---
def update_plant_level_from_excel(wind_df, solar_df, allowed_plant_dates=None):
    """
    Updates plant-level data and regenerates PDFs for each updated report.
    """
    csv_path = Config.CUSTOMER_DATA_CSV_PATH
    updated = {"both": 0, "solar": 0, "wind": 0}
    handled_hybrids = set()
    s, created = _get_session(None)
    try:
        # Aggregation
        wind_agg = wind_df[wind_df["Edit Action"] == "Updated"].groupby(["Date", "Plant ID"]).agg({
            "Edit Generation": "sum",
            "Edit AVG. Wind Speed": "mean",
            "Edit Generation Monthly": "sum",
            "Edit AVG. Wind Speed Monthly": "mean",
            "Comments": lambda x: x.dropna().astype(str).str.cat(sep="; ") if "Comments" in wind_df.columns else ""
        }).reset_index() if not wind_df.empty else pd.DataFrame(columns=["Date","Plant ID"])
        # Ensure Date column is datetime.date for correct comparison
        if not wind_agg.empty:
            wind_agg["Date"] = pd.to_datetime(wind_agg["Date"]).dt.date

        solar_agg = solar_df[solar_df["Edit Action"] == "Updated"].groupby(["Date", "Plant ID"]).agg({
            "Edit Generation": "sum",
            "Edit PR": "mean",
            "Edit POA": "mean",
            "Edit Generation Monthly": "sum",
            "Edit PR Monthly": "mean",
            "Edit POA Monthly": "mean",
            "Comments": lambda x: x.dropna().astype(str).str.cat(sep="; ") if "Comments" in solar_df.columns else ""
        }).reset_index() if not solar_df.empty else pd.DataFrame(columns=["Date","Plant ID"])

        # --- Turbine-wise JSON for wind and both tables ---
        wind_turbine_groups = {}
        if not wind_df.empty:
            wind_df_filtered = wind_df[wind_df["Edit Action"] == "Updated"]
            for (date, plant_id), group in wind_df_filtered.groupby(["Date", "Plant ID"]):
                arr = []
                for _, row in group.iterrows():
                    arr.append({
                        "Loc No": row.get("Turbine Name", ""),
                        "Avg Wind Speed": row.get("Edit AVG. Wind Speed", None),
                        "Daily Generation (KWh)": row.get("Edit Generation", None)
                    })
                wind_turbine_groups[(pd.to_datetime(date).date(), str(plant_id).strip())] = arr

        # --- BOTH/SOLAR ---
        for _, row in solar_agg.iterrows():
            plant_id, date_obj = row["Plant ID"], pd.to_datetime(row["Date"]).date()
            if allowed_plant_dates and (plant_id, date_obj) not in allowed_plant_dates:
                continue
            is_hybrid, hybrid_tag, wind_plant_id, plant_type, customer_name = get_hybrid_info_for_plant_id(plant_id, csv_path)
            if is_hybrid and plant_type == "solar":
                if (date_obj, hybrid_tag) in handled_hybrids:
                    continue
                record = s.query(DgrBothDb).filter_by(date=date_obj, plant_short_name_solar=plant_id, plant_short_name_wind=wind_plant_id).first()
                if not record or record.status == "Sent":
                    continue
                wind_row = wind_agg[(wind_agg["Date"] == date_obj) & (wind_agg["Plant ID"] == wind_plant_id)]
                if not (hasattr(record, "approved") and record.approved is True):
                    record.status = row.get("Edit Action", "Updated")
                record.edit_action = True
                record.edit_generation_solar = row["Edit Generation"]
                record.edit_pr = row["Edit PR"]
                record.edit_poa = row["Edit POA"]
                record.edit_generation_solar_monthly = row["Edit Generation Monthly"]
                record.edit_pr_monthly = row["Edit PR Monthly"]
                record.edit_poa_monthly = row["Edit POA Monthly"]
                if "Comments" in row:
                    try:
                        record.comments = row["Comments"]
                    except Exception:
                        pass
                if not wind_row.empty:
                    record.edit_generation_wind = wind_row["Edit Generation"].values[0]
                    record.edit_wind_speed = wind_row["Edit AVG. Wind Speed"].values[0]
                    record.edit_generation_wind_monthly = wind_row["Edit Generation Monthly"].values[0]
                    record.edit_wind_speed_monthly = wind_row["Edit AVG. Wind Speed Monthly"].values[0]
                turbine_json = wind_turbine_groups.get((date_obj, wind_plant_id), [])
                record.edit_csv_report_data = json.dumps(turbine_json, ensure_ascii=False)
                record.updated_at = datetime.now()
                updated["both"] += 1
                handled_hybrids.add((date_obj, hybrid_tag))
                # --- Regenerate PDF for BOTH ---
                edit_data = {
                    "edit_generation_solar": row["Edit Generation"],
                    "edit_pr": row["Edit PR"],
                    "edit_poa": row["Edit POA"],
                    "edit_generation_solar_monthly": row["Edit Generation Monthly"],
                    "edit_pr_monthly": row["Edit PR Monthly"],
                    "edit_poa_monthly": row["Edit POA Monthly"],
                    "edit_generation_wind": record.edit_generation_wind,
                    "edit_wind_speed": record.edit_wind_speed,
                    "edit_generation_wind_monthly": record.edit_generation_wind_monthly,
                    "edit_wind_speed_monthly": record.edit_wind_speed_monthly,
                    "edit_csv_report_data": record.edit_csv_report_data,
                    "edit_comments": record.comments
                }
                from app.import_turbine_inverter_data import edit_report_from_import
                edit_report_from_import("both", record.id, edit_data, db_session=s)
            else:
                record = s.query(SolarReport).filter_by(date=date_obj, plant_short_name=plant_id).first()
                if not record or record.status == "Sent":
                    continue
                if not (hasattr(record, "approved") and record.approved is True):
                    record.status = row.get("Edit Action", "Updated")
                record.edit_action = True
                record.edit_generation = row["Edit Generation"]
                record.edit_pr = row["Edit PR"]
                record.edit_poa = row["Edit POA"]
                record.edit_generation_monthly = row["Edit Generation Monthly"]
                record.edit_pr_monthly = row["Edit PR Monthly"]
                record.edit_poa_monthly = row["Edit POA Monthly"]
                if "Comments" in row:
                    try:
                        record.comments = row["Comments"]
                    except Exception:
                        pass
                record.updated_at = datetime.now()
                updated["solar"] += 1
                # --- Regenerate PDF for SOLAR ---
                edit_data = {
                    "edit_generation": row["Edit Generation"],
                    "edit_pr": row["Edit PR"],
                    "edit_poa": row["Edit POA"],
                    "edit_generation_monthly": row["Edit Generation Monthly"],
                    "edit_pr_monthly": row["Edit PR Monthly"],
                    "edit_poa_monthly": row["Edit POA Monthly"],
                    "edit_comments": record.comments
                }
                from app.import_turbine_inverter_data import edit_report_from_import
                edit_report_from_import("solar", record.id, edit_data, db_session=s)

        # --- WIND ---
        for _, row in wind_agg.iterrows():
            plant_id, date_obj = row["Plant ID"], pd.to_datetime(row["Date"]).date()
            if allowed_plant_dates and (plant_id, date_obj) not in allowed_plant_dates:
                continue
            is_hybrid, hybrid_tag, solar_plant_id, plant_type, customer_name = get_hybrid_info_for_plant_id(plant_id, csv_path)
            if is_hybrid and plant_type == "wind" and (date_obj, hybrid_tag) in handled_hybrids:
                continue
            record = s.query(WindReport).filter_by(date=date_obj, plant_short_name=plant_id).first()
            if not record or record.status == "Sent":
                continue
            print(f"[DEBUG] WindReport BEFORE update: plant_id={plant_id}, date={date_obj}, edit_generation={getattr(record, 'edit_generation', None)}, edit_action={getattr(record, 'edit_action', None)}")
            if not (hasattr(record, "approved") and record.approved is True):
                record.status = row.get("Edit Action", "Updated")
            # DO NOT update edit_* fields here! Only update status/comments if needed.
            if "Comments" in row:
                try:
                    record.comments = row["Comments"]
                except Exception:
                    pass
            turbine_json = wind_turbine_groups.get((date_obj, plant_id), [])
            edit_csv_report_data = json.dumps(turbine_json, ensure_ascii=False)
            record.updated_at = datetime.now()
            print(f"[DEBUG] WindReport AFTER update: plant_id={plant_id}, date={date_obj}, edit_generation={getattr(record, 'edit_generation', None)}, edit_action={getattr(record, 'edit_action', None)}")
            updated["wind"] += 1
            # --- Regenerate PDF for WIND ---
            edit_data = {
                "edit_generation": row["Edit Generation"],
                "edit_wind_speed": row["Edit AVG. Wind Speed"],
                "edit_generation_monthly": row["Edit Generation Monthly"],
                "edit_wind_speed_monthly": row["Edit AVG. Wind Speed Monthly"],
                "edit_csv_report_data": edit_csv_report_data,
                "edit_comments": record.comments
            }
            print(f"[DEBUG] Calling edit_report_from_import for wind: record_id={record.id}, edit_data={edit_data}")
            from app.import_turbine_inverter_data import edit_report_from_import
            edit_report_from_import("wind", record.id, edit_data, db_session=s)

        s.commit()
    except Exception:
        logger.exception("Error updating plant-level data")
        try:
            s.rollback()
        except:
            logger.exception("Rollback failed")
    finally:
        if s and created:
            s.close()
    return updated

# --- New: Edit report and regenerate PDF (single report, like edit_action in frontend_handler.py) ---
def edit_report_from_import(report_type, report_id, data, db_session=None):
    """
    Edit a DGR report (wind, solar, both) and regenerate its PDF, similar to edit_action in frontend_handler.py.

    Args:
        report_type (str): "wind", "solar", or "both"
        report_id (int): Report primary key
        data (dict): Dict of edit_* fields and values (e.g., edit_generation, edit_pr, etc.)
        db_session: Optional SQLAlchemy session

    Returns:
        dict: {"status": "success", "updated": report_dict} or error info

    Example usage (for testing):
        # Edit a wind report with id=123
        result, status = edit_report_from_import(
            report_type="wind",
            report_id=123,
            data={
                "edit_generation": 1000.0,
                "edit_wind_speed": 7.5,
                "edit_generation_monthly": 30000.0,
                "edit_wind_speed_monthly": 7.2,
                "edit_csv_report_data": '[{"Loc No": "T1", "Avg Wind Speed": 7.5, "Daily Generation (KWh)": 500}]'
            }
        )
        print(result, status)
    """


    s, created = _get_session(db_session)
    try:
        # Step 1: Get model class based on type
        model_map = {
            "solar": SolarReport,
            "wind": WindReport,
            "both": DgrBothDb
        }
        model = model_map.get(report_type)
        if not model:
            logger.error(f"Invalid report type: {report_type}")
            return {"error": "Invalid report type"}, 400

        report = s.get(model, report_id)
        if not report:
            logger.error(f"Report not found for type: {report_type}, id: {report_id}")
            return {"error": "Report not found"}, 404

        # Step 2: Determine if any edit_* values are actually different from the current edit_* values
        changes_made = False
        compare_fields = {
            "solar": [
                ("edit_generation", "generation"),
                ("edit_pr", "pr"),
                ("edit_poa", "poa"),
                ("edit_generation_monthly", "generation_monthly"),
                ("edit_pr_monthly", "pr_monthly"),
                ("edit_poa_monthly", "poa_monthly")
            ],
            "wind": [
                ("edit_generation", "generation"),
                ("edit_wind_speed", "wind_speed"),
                ("edit_generation_monthly", "generation_monthly"),
                ("edit_wind_speed_monthly", "wind_speed_monthly")
            ],
            "both": [
                ("edit_generation_solar", "generation_solar"),
                ("edit_pr", "pr"),
                ("edit_poa", "poa"),
                ("edit_generation_solar_monthly", "generation_solar_monthly"),
                ("edit_pr_monthly", "pr_monthly"),
                ("edit_poa_monthly", "poa_monthly"),
                ("edit_generation_wind", "generation_wind"),
                ("edit_wind_speed", "wind_speed"),
                ("edit_generation_wind_monthly", "generation_wind_monthly"),
                ("edit_wind_speed_monthly", "wind_speed_monthly")
            ]
        }

        changed_fields = []  # For audit log: list of (field, old, new)

        print(f"[DEBUG] edit_report_from_import: report_type={report_type}, report_id={report_id}")
        print(f"[DEBUG] Incoming data: {data}")
        print(f"[DEBUG] Current DB values: " +
              ", ".join([f"{edit_field}={getattr(report, edit_field, None)}" for edit_field, _ in compare_fields.get(report_type, [])]))

        # Compare incoming edit_* values to current edit_* values (not just original)
        for edit_field, original_field in compare_fields.get(report_type, []):
            incoming_val = data.get(edit_field, None)
            current_edit_val = getattr(report, edit_field, None)

            def normalize(val):
                if val is None or val == "":
                    return None
                try:
                    return float(val)
                except (ValueError, TypeError):
                    return val

            norm_incoming = normalize(incoming_val)
            norm_current_edit = normalize(current_edit_val)

            print(f"[DEBUG] Comparing field '{edit_field}': incoming={norm_incoming}, current={norm_current_edit}")

            if norm_incoming is None and norm_current_edit is None:
                continue  # both empty/none, not a change
            elif isinstance(norm_incoming, float) and isinstance(norm_current_edit, float):
                if round(norm_incoming, 4) != round(norm_current_edit, 4):
                    changes_made = True
                    changed_fields.append((edit_field, current_edit_val, incoming_val))
            else:
                if norm_incoming != norm_current_edit:
                    changes_made = True
                    changed_fields.append((edit_field, current_edit_val, incoming_val))

        # --- ADDITION: Check for changes in edit_csv_report_data for wind/both ---
        if report_type in ("wind", "both"):
            incoming_csv = data.get("edit_csv_report_data")
            current_csv = getattr(report, "edit_csv_report_data", None)
            if incoming_csv is not None and incoming_csv != current_csv:
                changes_made = True
                changed_fields.append(("edit_csv_report_data", "[previous csv data]", "[updated csv data]"))

        if changes_made:
            report.edit_action = True
            report.status = 'Updated'
            print(f"[DEBUG] edit_report_from_import: changes detected, setting edit_action=True for report_id={report_id}")
            logger.info(f"Edit action detected for report type: {report_type}, id: {report_id}")
        else:
            report.edit_action = False
            print(f"[DEBUG] edit_report_from_import: NO changes detected, setting edit_action=False for report_id={report_id}")
            logger.info(f"No changes detected for report type: {report_type}, id: {report_id}")
            if created:
                s.close()
            return {"status": "skipped", "message": "No changes detected."}, 200

        # Step 3: Update all edit_* fields from incoming data
        for key, value in data.items():
            if hasattr(report, key):
                try:
                    new_val = float(value) if value not in ("", None) else None
                except:
                    new_val = value
                setattr(report, key, new_val)

        # Step 4: Remove old PDF if it exists
        pdf_path = report.dgr_path
        if pdf_path and os.path.exists(pdf_path):
            try:
                os.remove(pdf_path)
                logger.info(f"Old PDF removed: {pdf_path}")
            except Exception as e:
                logger.warning(f"Could not remove old PDF: {e}")

        # Step 5: Regenerate the PDF
        date = str(getattr(report, "date"))

        # Save comments from edit modal (if present)
        if "edit_comments" in data:
            report.comments = data["edit_comments"]

        if report_type == "solar":
            generate_solar_dgr_pdf(
                date=date,
                customer_name=report.plant_long_name,
                poa=report.edit_poa or 0.0,
                pr=report.edit_pr or 0.0,
                daily_generation=report.edit_generation or 0.0,
                month_gen_value=report.edit_generation_monthly or 0.0,
                month_pr_value=report.edit_pr_monthly or 0.0,
                monthly_poa=report.edit_poa_monthly or 0.0,
                capacity=get_capacity_from_csv(report.plant_short_name),
                output_file=pdf_path,
                comment_text=report.comments if report.comments else None
            )
            logger.info(f"Solar PDF regenerated for report id: {report_id}")

        elif report_type == "wind":
            # Prefer edited CSV data if present, else original
            csv_data = report.edit_csv_report_data or report.csv_report_data
            if not csv_data:
                logger.error(f"No CSV data found for wind report id: {report_id}")
                if created:
                    s.close()
                return {"error": "No CSV data found for this report."}, 400

            # Ensure static/temp_csv directory exists
            static_csv_dir = os.path.join("static", "temp_csv")
            os.makedirs(static_csv_dir, exist_ok=True)

            # Write CSV data to a file in static/temp_csv
            csv_filename = f"report_{report_id}.csv"
            csv_path = os.path.join(static_csv_dir, csv_filename)
            try:
                # Try to detect and convert JSON-like string to CSV if needed
                needs_conversion = False
                try:
                    parsed = json.loads(csv_data)
                    if isinstance(parsed, list) and all(isinstance(row, dict) for row in parsed):
                        needs_conversion = True
                except Exception:
                    needs_conversion = False

                if needs_conversion:
                    with open(csv_path, "w", encoding="utf-8", newline='') as f:
                        writer = csv.DictWriter(f, fieldnames=["Loc No", "Avg Wind Speed", "Daily Generation (KWh)"])
                        writer.writeheader()
                        for row in parsed:
                            writer.writerow({
                                "Loc No": row.get("Loc No") or row.get("LocNo") or row.get("Loc_No") or "",
                                "Avg Wind Speed": row.get("Avg Wind Speed") or row.get("AvgWindSpeed") or row.get("Avg Wind Speed (m/s)") or "",
                                "Daily Generation (KWh)": row.get("Daily Generation (KWh)") or row.get("DailyGeneration") or row.get("Daily Generation (kWh)") or ""
                            })
                else:
                    with open(csv_path, "w", encoding="utf-8", newline='') as f:
                        f.write(csv_data)

                generate_combined_wind_pdf(
                    plant_name=report.plant_short_name,
                    start_date=date,
                    customer_name=report.plant_long_name,
                    project="WIND INTEGRUM",
                    avg_wind_speed=report.edit_wind_speed or 0.0,
                    total_generation=report.edit_generation or 0.0,
                    ma_percent=0.0,
                    monthly_wind=report.edit_wind_speed_monthly or 0.0,
                    monthly_generation=report.edit_generation_monthly or 0.0,
                    csv_filename=csv_path,
                    capacity=get_capacity_from_csv(report.plant_short_name),
                    output_file=pdf_path,
                    comment_text=report.comments if report.comments else None
                )
                logger.info(f"Wind PDF regenerated for report id: {report_id}")
            finally:
                try:
                    os.remove(csv_path)
                    logger.debug(f"Temp CSV file removed: {csv_path}")
                except Exception as e:
                    logger.warning(f"Could not remove temp CSV file: {e}")

        elif report_type == "both":
            # Prefer edited CSV data if present, else original
            csv_data = report.edit_csv_report_data or report.csv_report_data
            if not csv_data:
                logger.error(f"No CSV data found for both report id: {report_id}")
                if created:
                    s.close()
                return {"error": "No CSV data found for this report."}, 400

            # Ensure static/temp_csv directory exists
            static_csv_dir = os.path.join("static", "temp_csv")
            os.makedirs(static_csv_dir, exist_ok=True)

            # Write CSV data to a file in static/temp_csv
            csv_filename = f"report_{report_id}.csv"
            csv_path = os.path.join(static_csv_dir, csv_filename)
            wind_speed_df = None
            try:
                needs_conversion = False
                parsed = None
                try:
                    parsed = json.loads(csv_data)
                    if isinstance(parsed, list) and all(isinstance(row, dict) for row in parsed):
                        needs_conversion = True
                except Exception:
                    needs_conversion = False

                if needs_conversion:
                    with open(csv_path, "w", encoding="utf-8", newline='') as f:
                        writer = csv.DictWriter(f, fieldnames=["Loc No", "Avg Wind Speed", "Daily Generation (KWh)"])
                        writer.writeheader()
                        for row in parsed:
                            writer.writerow({
                                "Loc No": row.get("Loc No") or row.get("LocNo") or row.get("Loc_No") or "",
                                "Avg Wind Speed": row.get("Avg Wind Speed") or row.get("AvgWindSpeed") or row.get("Avg Wind Speed (m/s)") or "",
                                "Daily Generation (KWh)": row.get("Daily Generation (KWh)") or row.get("DailyGeneration") or row.get("Daily Generation (kWh)") or ""
                            })
                    # Also convert to DataFrame for PDF
                    wind_speed_df = pd.DataFrame(parsed)
                else:
                    with open(csv_path, "w", encoding="utf-8", newline='') as f:
                        f.write(csv_data)
                    # Try to read as DataFrame if possible
                    try:
                        wind_speed_df = pd.read_csv(csv_path)
                    except Exception:
                        wind_speed_df = pd.DataFrame()

                generate_combined_both_pdf(
                    date=date,
                    customer_name=report.plant_long_name_solar,
                    project="INTEGRUM",
                    daily_poa=report.edit_poa or 0.0,
                    daily_pr=report.edit_pr or 0.0,
                    daily_gen_solar=report.edit_generation_solar or 0.0,
                    month_gen_solar=report.edit_generation_solar_monthly or 0.0,
                    month_pr_solar=report.edit_pr_monthly or 0.0,
                    monthly_poa_solar=report.edit_poa_monthly or 0.0,
                    daily_wind_speed=report.edit_wind_speed or 0.0,
                    daily_gen_wind=report.edit_generation_wind or 0.0,
                    monthly_wind=report.edit_wind_speed_monthly or 0.0,
                    monthly_gen_wind=report.edit_generation_wind_monthly or 0.0,
                    wind_capacity=get_capacity_from_csv(report.plant_short_name_wind),
                    solar_capacity=get_capacity_from_csv(report.plant_short_name_solar),
                    csv_filename=csv_path,
                    plant_name_solar=report.plant_short_name_solar,
                    plant_name_wind=report.plant_short_name_wind,
                    output_file=pdf_path,
                    comment_text=report.comments if report.comments else None
                )
                logger.info(f"Both PDF regenerated for report id: {report_id}")
            finally:
                try:
                    os.remove(csv_path)
                    logger.debug(f"Temp BOTH CSV file removed: {csv_path}")
                except Exception as e:
                    logger.warning(f"Could not remove BOTH temp CSV file: {e}")

        # Step 6: Save the updates
        s.commit()
        logger.info(f"Report updated and committed for type: {report_type}, id: {report_id}")

        # --- AUDIT LOG FOR EDIT REPORT ---
        # (Optional: Add audit log here if needed)

        result = {"status": "success", "updated": report.to_dict()}
        if created:
            s.close()
        return result, 200

    except Exception as e:
        if created:
            s.rollback()
            s.close()
        logger.exception("Error in edit_report_from_import")
        return {"error": "Failed to update report", "details": str(e)}, 500

# --- Top-level import ---
def validate_excel_data(wind_df, solar_df, alarm_df):
    """
    Perform comprehensive validation on wind, solar, and alarm DataFrames.
    Raises ValueError with details if validation fails.
    """
    # --- Reference headers from export file ---
    WIND_SHEET_HEADERS = [
        "Date", "Plant ID", "Plant Name", "Turbine Name",
        "Prescinto Generation", "Prescinto  AVG. Wind Speed",
        "Prescinto Generation Monthly", "Prescinto  AVG. Wind Speed Monthly",
        "Edit Action", "Reason",
        "Edit Generation", "Edit AVG. Wind Speed",
        "Edit Generation Monthly", "Edit AVG. Wind Speed Monthly",
        "Comments"
    ]
    SOLAR_SHEET_HEADERS = [
        "Date", "Plant ID", "Plant Name", "Inverter Name",
        "Prescinto Generation", "Prescinto  PR", "Prescinto POA",
        "Prescinto  Generation Monthly", "Prescinto  PR Monthly", "Prescinto  POA Monthly",
        "Edit Action", "Reason",
        "Edit Generation", "Edit PR", "Edit POA",
        "Edit Generation Monthly", "Edit PR Monthly", "Edit POA Monthly",
        "Comments"
    ]
    ALARMS_SHEET_HEADERS = [
        "Date", "Plant Name", "Plant ID", "Alarm Name", "Controller Name",
        "Message", "Severity", "State", "Raised Time", "Resolved Time"
    ]
    # --- Helper for column checks ---
    def check_columns(df, expected, sheet):
        missing = [c for c in expected if c not in df.columns]
        extra = [c for c in df.columns if c not in expected]
        if missing:
            raise ValueError(f"{sheet} sheet missing columns: {missing}")
        if extra:
            raise ValueError(f"{sheet} sheet has unexpected columns: {extra}")

    # --- Helper for empty/duplicate checks ---
    def check_empty_and_duplicates(df, key_cols, sheet):
        if df.empty:
            raise ValueError(f"{sheet} sheet is empty.")
        # Check for empty critical fields
        for col in key_cols:
            if df[col].isnull().any() or (df[col].astype(str).str.strip() == "").any():
                raise ValueError(f"{sheet} sheet has empty values in required column: {col}")
        # Check for duplicates
        dups = df.duplicated(subset=key_cols, keep=False)
        if dups.any():
            raise ValueError(f"{sheet} sheet has duplicate rows based on {key_cols}: {df[dups][key_cols].to_dict('records')}")

    # --- Helper for type/range checks ---
    def check_types_and_ranges(df, col_types, col_ranges, sheet):
        for col, typ in col_types.items():
            if col in df.columns:
                # Only check non-empty
                for idx, val in df[col].items():
                    if pd.isnull(val) or str(val).strip() == "":
                        continue
                    try:
                        if typ == "float":
                            float(val)
                        elif typ == "int":
                            int(float(val))
                        elif typ == "date":
                            pd.to_datetime(val)
                    except Exception:
                        raise ValueError(f"{sheet} sheet: Invalid value '{val}' in column '{col}' (row {idx+2})")
        for col, (minv, maxv) in col_ranges.items():
            if col in df.columns:
                for idx, val in df[col].items():
                    if pd.isnull(val) or str(val).strip() == "":
                        continue
                    try:
                        v = float(val)
                        if (minv is not None and v < minv) or (maxv is not None and v > maxv):
                            raise ValueError(f"{sheet} sheet: Value {v} out of range in column '{col}' (row {idx+2})")
                    except Exception:
                        raise ValueError(f"{sheet} sheet: Invalid numeric value '{val}' in column '{col}' (row {idx+2})")

    # --- Wind sheet validation ---
    if not wind_df.empty:
        check_columns(wind_df, WIND_SHEET_HEADERS, "wind")
        check_empty_and_duplicates(wind_df, ["Date", "Plant ID", "Turbine Name"], "wind")
        # Edit Action validation
        allowed_actions = ["Updated", "Don't update", ""]
        if not wind_df["Edit Action"].isin(allowed_actions).all():
            bad = wind_df[~wind_df["Edit Action"].isin(allowed_actions)]
            raise ValueError(f"Wind sheet: Invalid 'Edit Action' values: {bad['Edit Action'].unique().tolist()}")
        # Date format
        try:
            pd.to_datetime(wind_df["Date"])
        except Exception:
            raise ValueError("Wind sheet: Invalid date format in 'Date' column.")
        # Numeric columns
        wind_float_cols = [
            "Prescinto Generation", "Prescinto  AVG. Wind Speed",
            "Prescinto Generation Monthly", "Prescinto  AVG. Wind Speed Monthly",
            "Edit Generation", "Edit AVG. Wind Speed",
            "Edit Generation Monthly", "Edit AVG. Wind Speed Monthly"
        ]
        wind_col_types = {col: "float" for col in wind_float_cols}
        wind_col_types["Date"] = "date"
        check_types_and_ranges(wind_df, wind_col_types, {
            "Edit Generation": (0, None),
            "Edit AVG. Wind Speed": (0, 100),
            "Edit Generation Monthly": (0, None),
            "Edit AVG. Wind Speed Monthly": (0, 100),
        }, "wind")

    # --- Solar sheet validation ---
    if not solar_df.empty:
        check_columns(solar_df, SOLAR_SHEET_HEADERS, "Solar")
        check_empty_and_duplicates(solar_df, ["Date", "Plant ID", "Inverter Name"], "Solar")
        allowed_actions = ["Updated", "Don't update", ""]
        if not solar_df["Edit Action"].isin(allowed_actions).all():
            bad = solar_df[~solar_df["Edit Action"].isin(allowed_actions)]
            raise ValueError(f"Solar sheet: Invalid 'Edit Action' values: {bad['Edit Action'].unique().tolist()}")
        try:
            pd.to_datetime(solar_df["Date"])
        except Exception:
            raise ValueError("Solar sheet: Invalid date format in 'Date' column.")
        solar_float_cols = [
            "Prescinto Generation", "Prescinto  PR", "Prescinto POA",
            "Prescinto  Generation Monthly", "Prescinto  PR Monthly", "Prescinto  POA Monthly",
            "Edit Generation", "Edit PR", "Edit POA",
            "Edit Generation Monthly", "Edit PR Monthly", "Edit POA Monthly"
        ]
        solar_col_types = {col: "float" for col in solar_float_cols}
        solar_col_types["Date"] = "date"
        check_types_and_ranges(solar_df, solar_col_types, {
            "Edit Generation": (0, None),
            "Edit PR": (0, 100),
            "Edit POA": (0, None),
            "Edit Generation Monthly": (0, None),
            "Edit PR Monthly": (0, 100),
            "Edit POA Monthly": (0, None),
        }, "Solar")

    # --- Alarm sheet validation ---
    if not alarm_df.empty:
        check_columns(alarm_df, ALARMS_SHEET_HEADERS, "Alarm")
        check_empty_and_duplicates(alarm_df, ["Date", "Plant ID", "Alarm Name", "Raised Time"], "Alarm")
        # Date/time columns
        for col in ["Date", "Raised Time"]:
            try:
                pd.to_datetime(alarm_df[col])
            except Exception:
                raise ValueError(f"Alarm sheet: Invalid date/time format in '{col}' column.")
        # Severity/State allowed values (optional: can be extended)
        # No further checks for now

def import_turbine_inverter_edits(excel_path: str):
    """
    Import turbine/inverter edits from Excel and return update counts.

    Returns:
        dict: {
            "turbine_updated": int,
            "inverter_updated": int,
            "turbine_inverter_updated": int,  # (sum, for backward compatibility)
            "plant_level_updated": {"wind": int, "solar": int, "both": int},
            "alarms_inserted": int,
            "stovekraft_paired": int
        }
    """
    try:
        xls = pd.ExcelFile(excel_path)
    except Exception:
        logger.exception("Failed to read Excel file")
        raise
    allowed_plant_dates = get_allowed_plant_dates_from_status()
    wind_df = pd.read_excel(xls, sheet_name=WIND_SHEET) if WIND_SHEET in xls.sheet_names else pd.DataFrame()
    solar_df = pd.read_excel(xls, sheet_name=SOLAR_SHEET) if SOLAR_SHEET in xls.sheet_names else pd.DataFrame()
    alarm_df = pd.read_excel(xls, sheet_name="alarm") if "alarm" in xls.sheet_names else pd.DataFrame()

    # --- VALIDATION STEP ---
    validate_excel_data(wind_df, solar_df, alarm_df)

    turbine_updated = 0
    inverter_updated = 0
    alarms_inserted = 0

    s, created = _get_session()
    try:
        turbine_updated = update_wind_from_excel(wind_df, db_session=s, allowed_plant_dates=allowed_plant_dates)
        inverter_updated = update_solar_from_excel(solar_df, db_session=s, allowed_plant_dates=allowed_plant_dates)
        # Import alarms if alarm_df is not empty and has required columns
        if not alarm_df.empty and all(col in alarm_df.columns for col in [
            "Date", "Plant Name", "Plant ID", "Alarm Name", "Controller Name",
            "Message", "Severity", "State", "Raised Time", "Resolved Time"
        ]):
            alarms_inserted = import_alarm_data_from_df(alarm_df, db_session=s)
        s.commit()
    except:
        s.rollback()
        raise
    finally:
        if created:
            s.close()

    # Plant-level updates
    plant_level_updated = update_plant_level_from_excel(wind_df, solar_df, allowed_plant_dates)

    # --- Stovekraft, Jodhani, Balaji Pairing (trigger only for updated rows) ---
    stovekraft_paired = 0
    jodhani_balaji_paired = 0

    # Collect updated dates for Stovekraft, Jodhani, Balaji from both solar and wind sheets
    stovekraft_plants = {"IN.INTE.SKRT", "IN.INTE.SKRT2"}
    jodhani_plants = {"IN.INTE.JOD1", "IN.INTE.JOD2"}
    balaji_plants = {"IN.INTE.BAL1", "IN.INTE.BAL2"}

    # Helper to get updated dates for a set of plants from a DataFrame
    def get_updated_dates(df, plant_set, id_col="Plant ID"):
        updated_dates = set()
        if not df.empty and id_col in df.columns and "Edit Action" in df.columns:
            for _, row in df[df["Edit Action"] == "Updated"].iterrows():
                plant_id = str(row.get(id_col, "")).strip()
                if plant_id in plant_set:
                    try:
                        date_obj = pd.to_datetime(row["Date"]).date()
                        updated_dates.add(date_obj)
                    except Exception:
                        continue
        return updated_dates

    # Get all updated dates for each group from both solar and wind sheets
    stovekraft_dates = get_updated_dates(solar_df, stovekraft_plants)
    # Jodhani/Balaji: union of updated dates from solar and wind
    jodhani_dates = get_updated_dates(solar_df, jodhani_plants) | get_updated_dates(wind_df, {"IN.INTE.JPPL"})
    balaji_dates = get_updated_dates(solar_df, balaji_plants) | get_updated_dates(wind_df, {"IN.INTE.BMPL"})

    # Pair Stovekraft for updated dates
    for date_obj in stovekraft_dates:
        try:
            result = pair_stovekraft_solar_reports(date_obj)
            if result.get("paired", False):
                stovekraft_paired += 1
                logger.info(f"Paired Stovekraft reports for {date_obj}")
        except Exception:
            logger.exception(f"Failed to pair Stovekraft reports for {date_obj}")

    # Pair Jodhani for updated dates
    for date_obj in jodhani_dates:
        try:
            jodhani_result = pair_jodhani_both_reports(date_obj, wind_df=wind_df)
            if jodhani_result.get("paired", False):
                jodhani_balaji_paired += 1
                logger.info(f"Paired Jodhani both reports for {date_obj}")
        except Exception:
            logger.exception(f"Failed to pair Jodhani both reports for {date_obj}")

    # Pair Balaji for updated dates
    for date_obj in balaji_dates:
        try:
            balaji_result = pair_balaji_both_reports(date_obj, wind_df=wind_df)
            if balaji_result.get("paired", False):
                jodhani_balaji_paired += 1
                logger.info(f"Paired Balaji both reports for {date_obj}")
        except Exception:
            logger.exception(f"Failed to pair Balaji both reports for {date_obj}")

    # Note: Pairing is now only triggered for dates where data was actually updated (Edit Action == "Updated")
    # and for both solar and wind updates for Jodhani/Balaji.

    return {
        "turbine_updated": turbine_updated,
        "inverter_updated": inverter_updated,
        "turbine_inverter_updated": turbine_updated + inverter_updated,
        "plant_level_updated": plant_level_updated,
        "alarms_inserted": alarms_inserted,
        "stovekraft_paired": stovekraft_paired,
        "jodhani_balaji_paired": jodhani_balaji_paired
    }

# --- Alarm import function ---
def import_alarm_data_from_df(alarm_df: pd.DataFrame, db_session=None):
    """
    Import alarm data from a DataFrame into the plant_alarms table.
    Expects columns: Date, Plant Name, Plant ID, Alarm Name, Controller Name, Message, Severity, State, Raised Time, Resolved Time
    Duration is calculated as (Resolved Time - Raised Time) in minutes.
    """
    s, created = _get_session(db_session)
    inserted = 0
    try:
        for idx, row in alarm_df.iterrows():
            try:
                alarm_date = pd.to_datetime(row["Date"]).date()
                plant_name = str(row["Plant Name"])
                plant_id = str(row["Plant ID"])
                alarm_name = str(row["Alarm Name"])
                controller_name = str(row["Controller Name"]) if pd.notnull(row["Controller Name"]) else None
                message = str(row["Message"]) if pd.notnull(row["Message"]) else None
                severity = str(row["Severity"]) if pd.notnull(row["Severity"]) else None
                state = str(row["State"]) if pd.notnull(row["State"]) else None
                raised_time = pd.to_datetime(row["Raised Time"])
                resolved_time = pd.to_datetime(row["Resolved Time"]) if pd.notnull(row["Resolved Time"]) else None
                duration_minutes = None
                if resolved_time is not None:
                    duration_minutes = int((resolved_time - raised_time).total_seconds() // 60)
                alarm = PlantAlarm(
                    alarm_date=alarm_date,
                    plant_name=plant_name,
                    plant_id=plant_id,
                    alarm_name=alarm_name,
                    controller_name=controller_name,
                    message=message,
                    severity=severity,
                    state=state,
                    raised_time=raised_time,
                    resolved_time=resolved_time,
                    duration_minutes=duration_minutes
                )
                s.add(alarm)
                inserted += 1
            except Exception:
                logger.exception("Error processing alarm row %d", idx)
        if created:
            s.commit()
    finally:
        if created:
            s.close()
    return inserted



def update_stovekraft_combined_report(
    date_obj: datetime.date,
    poa,
    pr,
    daily_generation,
    month_gen_value,
    month_pr_value,
    monthly_poa,
    comment_text=None,
    edit_generation=None,
    edit_pr=None,
    edit_poa=None,
    edit_generation_monthly=None,
    edit_pr_monthly=None,
    edit_poa_monthly=None,
    db_session=None
):
    
    print("========== UPDATE STOVEKRAFT COMBINED REPORT ==========")
    print(f"INPUTS: date={date_obj}, poa={poa}, pr={pr}, gen={daily_generation}, "
          f"month_gen={month_gen_value}, month_pr={month_pr_value}, month_poa={monthly_poa}, "
          f"comment={comment_text}")

    from DB.setup_db import session  # ✅ Use scoped session
    combined_plant_id = "IN.INTE.SKRT&IN.INTE.SKRT2"
    combined_plant_name = "Stovekraft Limited Unit 1 & 2"

    # Allow external session if passed
    s = db_session or session

    try:
        print("[STEP 1] Querying combined report...")
        report = s.query(SolarReport).filter_by(
            date=date_obj,
            plant_short_name=combined_plant_id
        ).first()

        if not report:
            print(f"[ERROR] No combined report found for {date_obj} ({combined_plant_id})")
            return {"status": "error", "message": "Combined report not found for update."}

        print(f"[FOUND] Report ID: {getattr(report, 'id', None)}, Approved: {getattr(report, 'approved', None)}")

        if getattr(report, "approved", False):
            print("[SKIP] Report already approved. No update allowed.")
            return {"status": "error", "message": "Report already approved."}

        print(f"[BEFORE UPDATE] poa={report.poa}, pr={report.pr}, gen={report.generation}, "
              f"month_gen={report.generation_monthly}, month_pr={report.pr_monthly}, month_poa={report.poa_monthly}")

        # 🔹 Update values
        report.poa = poa
        report.pr = pr
        report.generation = daily_generation
        report.generation_monthly = month_gen_value
        report.pr_monthly = month_pr_value
        report.poa_monthly = monthly_poa
        report.comments = comment_text
        report.status = "Updated"
        report.edit_action = True

        # --- Update edit_ fields if provided ---
        if edit_generation is not None:
            report.edit_generation = edit_generation
        if edit_pr is not None:
            report.edit_pr = edit_pr
        if edit_poa is not None:
            report.edit_poa = edit_poa
        if edit_generation_monthly is not None:
            report.edit_generation_monthly = edit_generation_monthly
        if edit_pr_monthly is not None:
            report.edit_pr_monthly = edit_pr_monthly
        if edit_poa_monthly is not None:
            report.edit_poa_monthly = edit_poa_monthly

        print("[STEP 2] Values updated in memory (pre-commit). Flushing...")
        s.flush()  # force SQL emission before commit
        print("[OK] Flush done. Committing now...")
        s.commit()
        print("[OK] Commit done.")

        # Verify values from DB
        print("[STEP 3] Refreshing record to verify database persistence...")
        s.refresh(report)
        print(f"[AFTER COMMIT] poa={report.poa}, pr={report.pr}, gen={report.generation}, "
              f"month_gen={report.generation_monthly}, month_pr={report.pr_monthly}, month_poa={report.poa_monthly}")

    except Exception as e:
        print(f"[EXCEPTION] Rolling back due to: {e}")
        s.rollback()
        return {"status": "error", "message": str(e)}

    # 🔹 Regenerate PDF after DB commit
    try:
        print("[STEP 4] Starting PDF generation...")
        pdf_dir = "static/solar_final_report"
        os.makedirs(pdf_dir, exist_ok=True)
        pdf_path = os.path.join(pdf_dir, f"{combined_plant_id}_DGR_{date_obj}.jpg")

        capacity = (get_capacity_from_csv("IN.INTE.SKRT") or 0.0) + (get_capacity_from_csv("IN.INTE.SKRT2") or 0.0)
        print(f"[PDF PARAMS] capacity={capacity}, output={pdf_path}")

        generate_solar_dgr_pdf(
            date=str(date_obj),
            customer_name=combined_plant_name,
            poa=poa,
            pr=pr,
            daily_generation=daily_generation,
            month_gen_value=month_gen_value,
            month_pr_value=month_pr_value,
            monthly_poa=monthly_poa,
            capacity=capacity,
            output_file=pdf_path,
            comment_text=comment_text
        )
        print("[OK] PDF generated successfully.")

        # ✅ Update DB with PDF path and commit again
        report.dgr_path = pdf_path
        s.flush()
        s.commit()
        print(f"[FINAL COMMIT] PDF path updated in DB: {pdf_path}")

        print("========== UPDATE SUCCESSFULLY COMPLETED ==========")
        return {"status": "success", "message": "Combined report updated and PDF regenerated."}

    except Exception as pdf_error:
        print(f"[PDF ERROR] PDF generation failed: {pdf_error}")
        s.rollback()
        return {"status": "warning", "message": "Report updated, but PDF generation failed."}

    finally:
        print("[SESSION] Scoped session managed by framework  not closing manually.")



def pair_stovekraft_solar_reports(date_obj: datetime.date, db_session=None):
    """
    Pair solar reports for Stovekraft plants (IN.INTE.SKRT & IN.INTE.SKRT2)
    into a single combined report with plant name "Stovekraft Limited Unit 1 & 2".

    This function:
    1. Finds existing separate reports for both plant IDs on the given date
    2. Combines their data (sums generation values, averages PR/POA values)
    3. Creates a new combined report with plant_short_name as "IN.INTE.SKRT&IN.INTE.SKRT2"
    4. Updates plant_long_name to "Stovekraft Limited Unit 1 & 2"
    5. Keeps the original individual reports (does NOT delete them)

    Note: This creates an additional combined report while preserving the individual
    plant-level data for IN.INTE.SKRT and IN.INTE.SKRT2.

    Args:
        date_obj (datetime.date): The date for which to pair the reports
        db_session: Optional SQLAlchemy session

    Returns:
        dict: {"status": "success", "paired": True/False, "message": str}
    """
    s, created = _get_session(db_session)

    try:
        plant_id_1 = "IN.INTE.SKRT"
        plant_id_2 = "IN.INTE.SKRT2"
        combined_plant_id = f"{plant_id_1}&{plant_id_2}"
        combined_plant_name = "Stovekraft Limited Unit 1 & 2"

        # Check if combined report already exists
        existing_combined = s.query(SolarReport).filter_by(
            date=date_obj,
            plant_short_name=combined_plant_id
        ).first()

        if existing_combined:
            # If the report exists and is not sent, update it using update_stovekraft_combined_report
            if getattr(existing_combined, "status", None) != "Sent":
                # Find individual reports for both plants
                report_1 = s.query(SolarReport).filter_by(
                    date=date_obj,
                    plant_short_name=plant_id_1
                ).first()

                report_2 = s.query(SolarReport).filter_by(
                    date=date_obj,
                    plant_short_name=plant_id_2
                ).first()

                if not report_1 or not report_2:
                    logger.warning(f"Cannot update combined Stovekraft report for {date_obj}: missing individual report(s)")
                    return {
                        "status": "error",
                        "paired": False,
                        "can_update": True,
                        "message": f"Cannot update combined report for {date_obj}: missing individual report(s)"
                    }

                # Compute combined values (sum generation, but AVERAGE PR and POA as they are ratios)
                poa = ((report_1.poa or 0) + (report_2.poa or 0)) / 2
                pr = round(((report_1.pr or 0) + (report_2.pr or 0)) / 2, 2)
                daily_generation = (report_1.generation or 0) + (report_2.generation or 0)
                month_gen_value = (report_1.generation_monthly or 0) + (report_2.generation_monthly or 0)
                month_pr_value = round(((report_1.pr_monthly or 0) + (report_2.pr_monthly or 0)) / 2, 2)
                monthly_poa = ((report_1.poa_monthly or 0) + (report_2.poa_monthly or 0)) / 2
                comment_text = None

                # If both have comments, join them; else use whichever is present
                if getattr(report_1, "comments", None) and getattr(report_2, "comments", None):
                    comment_text = f"{report_1.comments}; {report_2.comments}"
                elif getattr(report_1, "comments", None):
                    comment_text = report_1.comments
                elif getattr(report_2, "comments", None):
                    comment_text = report_2.comments

                # Compute edit_ fields (sum/average) if present
                edit_generation = None
                edit_pr = None
                edit_poa = None
                edit_generation_monthly = None
                edit_pr_monthly = None
                edit_poa_monthly = None
                if (hasattr(report_1, 'edit_generation') and report_1.edit_generation is not None and
                    hasattr(report_2, 'edit_generation') and report_2.edit_generation is not None):
                    edit_generation = (report_1.edit_generation or 0) + (report_2.edit_generation or 0)
                    edit_pr = round(((report_1.edit_pr or 0) + (report_2.edit_pr or 0)) / 2, 2)
                    edit_poa = ((report_1.edit_poa or 0) + (report_2.edit_poa or 0)) / 2
                    edit_generation_monthly = (report_1.edit_generation_monthly or 0) + (report_2.edit_generation_monthly or 0)
                    edit_pr_monthly = round(((report_1.edit_pr_monthly or 0) + (report_2.edit_pr_monthly or 0)) / 2, 2)
                    edit_poa_monthly = ((report_1.edit_poa_monthly or 0) + (report_2.edit_poa_monthly or 0)) / 2

                update_result = update_stovekraft_combined_report(
                    date_obj=date_obj,
                    poa=poa,
                    pr=pr,
                    daily_generation=daily_generation,
                    month_gen_value=month_gen_value,
                    month_pr_value=month_pr_value,
                    monthly_poa=monthly_poa,
                    comment_text=comment_text,
                    edit_generation=edit_generation,
                    edit_pr=edit_pr,
                    edit_poa=edit_poa,
                    edit_generation_monthly=edit_generation_monthly,
                    edit_pr_monthly=edit_pr_monthly,
                    edit_poa_monthly=edit_poa_monthly,
                    db_session=s
                )

                logger.info(f"Updated existing combined Stovekraft report for {date_obj}")

                return {
                    "status": "updated",
                    "paired": False,
                    "can_update": True,
                    "message": f"Combined report updated for {date_obj}",
                    "update_result": update_result
                }
            logger.info(f"Combined Stovekraft report already exists and is sent for {date_obj}")
            return {
                "status": "success",
                "paired": False,
                "can_update": False,
                "message": f"Combined report already exists and is sent for {date_obj}"
            }

        # Find individual reports for both plants
        report_1 = s.query(SolarReport).filter_by(
            date=date_obj,
            plant_short_name=plant_id_1
        ).first()

        report_2 = s.query(SolarReport).filter_by(
            date=date_obj,
            plant_short_name=plant_id_2
        ).first()

        if not report_1 and not report_2:
            logger.info(f"No individual Stovekraft reports found for {date_obj}")
            return {
                "status": "success",
                "paired": False,
                "message": f"No individual reports found for {date_obj}"
            }

        # If only one report exists, we can't pair
        if not report_1 or not report_2:
            missing_plant = plant_id_1 if not report_1 else plant_id_2
            logger.warning(f"Missing report for {missing_plant} on {date_obj}, cannot pair")
            return {
                "status": "success",
                "paired": False,
                "message": f"Missing report for {missing_plant}, cannot pair"
            }

        # Combine the data from both reports
        # Sum generation values, but AVERAGE PR and POA values (they are ratios/percentages)
        combined_data = {
            "date": date_obj,
            "plant_short_name": combined_plant_id,
            "plant_long_name": combined_plant_name,
            "generation": (report_1.generation or 0) + (report_2.generation or 0),
            "pr": ((report_1.pr or 0) + (report_2.pr or 0)) / 2,
            "poa": ((report_1.poa or 0) + (report_2.poa or 0)) / 2,
            "generation_monthly": (report_1.generation_monthly or 0) + (report_2.generation_monthly or 0),
            "pr_monthly": ((report_1.pr_monthly or 0) + (report_2.pr_monthly or 0)) / 2,
            "poa_monthly": ((report_1.poa_monthly or 0) + (report_2.poa_monthly or 0)) / 2,

            # Combine edit fields if they exist
            "edit_generation": None,
            "edit_pr": None,
            "edit_poa": None,
            "edit_generation_monthly": None,
            "edit_pr_monthly": None,
            "edit_poa_monthly": None,

            # Copy other fields from the first report (or use defaults)
            "approved": report_1.approved if hasattr(report_1, 'approved') else False,
            "review": report_1.review if hasattr(report_1, 'review') else False,
            "action_performed": report_1.action_performed if hasattr(report_1, 'action_performed') else False,
            "status": report_1.status if hasattr(report_1, 'status') else "Pending",
            "edit_action": False,
            "comments": None,
            "dgr_path": None,  # Will need to be regenerated
            "regenerate": False,
            "dont_send": False,
            "save_action": False,
            "saved_count": 0
        }

        # Handle edit fields if both reports have them
        if (hasattr(report_1, 'edit_generation') and report_1.edit_generation is not None and
            hasattr(report_2, 'edit_generation') and report_2.edit_generation is not None):
            combined_data.update({
                "edit_generation": (report_1.edit_generation or 0) + (report_2.edit_generation or 0),
                # For Stovekraft, PR and POA should be mean, not sum
                "edit_pr": ((report_1.edit_pr or 0) + (report_2.edit_pr or 0)) / 2,
                "edit_poa": ((report_1.edit_poa or 0) + (report_2.edit_poa or 0)) / 2,
                "edit_generation_monthly": (report_1.edit_generation_monthly or 0) + (report_2.edit_generation_monthly or 0),
                "edit_pr_monthly": ((report_1.edit_pr_monthly or 0) + (report_2.edit_pr_monthly or 0)) / 2,
                "edit_poa_monthly": ((report_1.edit_poa_monthly or 0) + (report_2.edit_poa_monthly or 0)) / 2,
                "edit_action": True
            })

        # Create the combined report
        combined_report = SolarReport(**combined_data)
        s.add(combined_report)
        s.flush()  # Get the ID

        # Generate PDF path for the combined report
        pdf_dir = "static/solar_final_report"
        os.makedirs(pdf_dir, exist_ok=True)
        pdf_path = os.path.join(pdf_dir, f"{combined_plant_id}_DGR_{date_obj}.jpg")
        combined_report.dgr_path = pdf_path

        try:
            from helper.utils import generate_solar_dgr_pdf, get_capacity_from_csv
            solar_capacity = int(get_capacity_from_csv(plant_id_1) or 0.0) + int(get_capacity_from_csv(plant_id_2) or 0.0)
            generate_solar_dgr_pdf(
                date=str(date_obj),
                customer_name=combined_plant_name,
                poa=combined_report.poa or 0.0,
                pr=combined_report.pr or 0.0,
                daily_generation=combined_report.generation or 0.0,
                month_gen_value=combined_report.generation_monthly or 0.0,
                month_pr_value=combined_report.pr_monthly or 0.0,
                monthly_poa=combined_report.poa_monthly or 0.0,
                capacity=solar_capacity,
                output_file=pdf_path,
                comment_text=None
            )
            logger.info(f"Generated combined Stovekraft PDF at {pdf_path}")
            # Optionally, upload to S3 if needed (uncomment if required)
            # from helper.storage_s3 import upload_file_s3
            # s3_path = f"solar_reports/{combined_plant_id}_DGR_{date_obj}.jpg"
            # upload_file_s3(pdf_path, s3_path)
        except Exception as e:
            logger.error(f"Failed to generate combined Stovekraft PDF: {e}")

        # Keep the original individual reports - DO NOT DELETE THEM
        # This allows maintaining both individual plant data and the combined report

        # Commit the changes
        if created:
            s.commit()

        logger.info(f"Successfully created combined Stovekraft report for {date_obj} (individual reports preserved)")
        return {
            "status": "success",
            "paired": True,
            "message": f"Successfully created combined report for {date_obj} (individual reports preserved)",
            "combined_report_id": combined_report.id
        }

    except Exception as e:
        if created:
            s.rollback()
        logger.exception(f"Error pairing Stovekraft reports for {date_obj}")
        return {
            "status": "error",
            "paired": False,
            "message": f"Error pairing reports: {str(e)}"
        }
    finally:
        if created:
            s.close()





def pair_stovekraft_reports_for_date_range(start_date: str, end_date: str = None):
    """
    Pair Stovekraft solar reports for a date range.

    Args:
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str, optional): End date in YYYY-MM-DD format. If None, only process start_date

    Returns:
        dict: {"total_paired": int, "results": [{"date": str, "result": dict}]}
    """
    try:
        start_date_obj = pd.to_datetime(start_date).date()
        end_date_obj = pd.to_datetime(end_date).date() if end_date else start_date_obj
    except Exception:
        logger.error(f"Invalid date format. Use YYYY-MM-DD format.")
        return {"total_paired": 0, "results": [], "error": "Invalid date format"}

    if start_date_obj > end_date_obj:
        logger.error("Start date cannot be after end date")
        return {"total_paired": 0, "results": [], "error": "Start date cannot be after end date"}

    total_paired = 0
    results = []
    current_date = start_date_obj

    while current_date <= end_date_obj:
        try:
            result = pair_stovekraft_solar_reports(current_date)
            results.append({"date": str(current_date), "result": result})
            if result.get("paired", False):
                total_paired += 1
        except Exception as e:
            results.append({
                "date": str(current_date),
                "result": {"status": "error", "paired": False, "message": str(e)}
            })

        # Move to next day
        current_date = current_date + pd.Timedelta(days=1).to_pytimedelta()

    logger.info(f"Stovekraft pairing completed. Total paired: {total_paired}")
    return {"total_paired": total_paired, "results": results}

# --- Jodhani Both Pairing Function ---
def pair_jodhani_both_reports(date_obj: datetime.date, wind_df=None, db_session=None):
    """
    Pair solar reports for Jodhani plants (IN.INTE.JOD1 & IN.INTE.JOD2)
    into a single combined report in the both table (DgrBothDb).

    This function:
    1. Finds existing separate solar reports for both plant IDs on the given date
    2. Combines their data (sums generation values, averages PR/POA values)
    3. Creates a new combined report in DgrBothDb with plant_short_name_solar as "IN.INTE.JOD1&IN.INTE.JOD2"
    4. Updates plant_long_name_solar to "Jodhani 1 & 2 Papers Pvt Ltd"
    5. Keeps the original individual reports (does NOT delete them)

    Args:
        date_obj (datetime.date): The date for which to pair the reports
        db_session: Optional SQLAlchemy session

    Returns:
        dict: {"status": "success", "paired": True/False, "message": str}
    """
    s, created = _get_session(db_session)

    try:
        plant_id_1 = "IN.INTE.JOD1"
        plant_id_2 = "IN.INTE.JOD2"
        combined_plant_id = f"{plant_id_1}&{plant_id_2}"
        combined_plant_name = "Jodhani 1 & 2 Papers Pvt Ltd"
        wind_plant_id = "IN.INTE.JPPL"  # From CSV: Jodhani wind plant
        wind_plant_name = "Jodhani wind"

        # Check if combined report already exists in both table
        existing_combined = s.query(DgrBothDb).filter_by(
            date=date_obj,
            plant_short_name_solar=combined_plant_id,
            plant_short_name_wind=wind_plant_id
        ).first()

        if existing_combined:
            # If the report exists and is not sent, update it
            if getattr(existing_combined, "status", None) != "Sent":
                # Check if report is approved - if approved, skip update
                if getattr(existing_combined, "approved", False):
                    logger.info(f"Combined Jodhani both report for {date_obj} is approved, skipping update")
                    return {
                        "status": "approved",
                        "paired": False,
                        "message": f"Combined both report for {date_obj} is approved and cannot be updated"
                    }

                # Find individual solar reports for both plants to update the combined report
                report_1 = s.query(SolarReport).filter_by(
                    date=date_obj,
                    plant_short_name=plant_id_1
                ).first()

                # JOD2 might be in DgrBothDb as individual hybrid record
                report_2 = s.query(SolarReport).filter_by(
                    date=date_obj,
                    plant_short_name=plant_id_2
                ).first()

                # If not found in SolarReport, check DgrBothDb for JOD2
                if not report_2:
                    both_record_2 = s.query(DgrBothDb).filter_by(
                        date=date_obj,
                        plant_short_name_solar=plant_id_2
                    ).first()
                    if both_record_2:
                        # Create a mock SolarReport-like object from DgrBothDb data
                        class MockSolarReport:
                            def __init__(self, both_record):
                                self.generation = both_record.generation_solar
                                self.pr = both_record.pr
                                self.poa = both_record.poa
                                self.generation_monthly = both_record.generation_solar_monthly
                                self.pr_monthly = both_record.pr_monthly
                                self.poa_monthly = both_record.poa_monthly
                                self.edit_generation = both_record.edit_generation_solar
                                self.edit_pr = both_record.edit_pr
                                self.edit_poa = both_record.edit_poa
                                self.edit_generation_monthly = both_record.edit_generation_solar_monthly
                                self.edit_pr_monthly = both_record.edit_pr_monthly
                                self.edit_poa_monthly = both_record.edit_poa_monthly
                                self.comments = both_record.comments

                        report_2 = MockSolarReport(both_record_2)

                if not report_1 or not report_2:
                    logger.warning(f"Cannot update combined Jodhani both report for {date_obj}: missing individual solar report(s)")
                    return {
                        "status": "error",
                        "paired": False,
                        "message": f"Cannot update combined report for {date_obj}: missing individual solar report(s)"
                    }

                # Get wind data from DataFrame
                wind_data = None
                wind_turbine_json = None
                if wind_df is not None and not wind_df.empty:
                    # Aggregate wind data for the specific plant and date
                    wind_filtered = wind_df[
                        (wind_df["Plant ID"] == wind_plant_id) &
                        (pd.to_datetime(wind_df["Date"]).dt.date == date_obj) &
                        (wind_df["Edit Action"] == "Updated")
                    ]
                    if not wind_filtered.empty:
                        # Turbine-wise JSON
                        wind_turbine_json = [
                            {
                                "Loc No": row.get("Turbine Name", ""),
                                "Avg Wind Speed": row.get("Edit AVG. Wind Speed", None),
                                "Daily Generation (KWh)": row.get("Edit Generation", None)
                            }
                            for _, row in wind_filtered.iterrows()
                        ]
                        wind_data = {
                            "generation": wind_filtered["Edit Generation"].sum(),
                            "wind_speed": wind_filtered["Edit AVG. Wind Speed"].mean(),
                            "generation_monthly": wind_filtered["Edit Generation Monthly"].sum(),
                            "wind_speed_monthly": wind_filtered["Edit AVG. Wind Speed Monthly"].mean(),
                            "csv_report_data": json.dumps(wind_turbine_json, ensure_ascii=False)
                        }

                # Update existing combined report with new data
                # Combine the data from both solar reports (sum generation, average PR/POA)
                existing_combined.generation_solar = (report_1.generation or 0) + (report_2.generation or 0)
                existing_combined.pr = round(((report_1.pr or 0) + (report_2.pr or 0)) / 2, 2)
                existing_combined.poa = ((report_1.poa or 0) + (report_2.poa or 0)) / 2
                existing_combined.generation_solar_monthly = (report_1.generation_monthly or 0) + (report_2.generation_monthly or 0)
                existing_combined.pr_monthly = round(((report_1.pr_monthly or 0) + (report_2.pr_monthly or 0)) / 2, 2)
                existing_combined.poa_monthly = ((report_1.poa_monthly or 0) + (report_2.poa_monthly or 0)) / 2

                # Handle edit fields if both reports have them
                if (hasattr(report_1, 'edit_generation') and report_1.edit_generation is not None and
                    hasattr(report_2, 'edit_generation') and report_2.edit_generation is not None):
                    existing_combined.edit_generation_solar = (report_1.edit_generation or 0) + (report_2.edit_generation or 0)
                    existing_combined.edit_pr = round(((report_1.edit_pr or 0) + (report_2.edit_pr or 0)) / 2, 2)
                    existing_combined.edit_poa = ((report_1.edit_poa or 0) + (report_2.edit_poa or 0)) / 2
                    existing_combined.edit_generation_solar_monthly = (report_1.edit_generation_monthly or 0) + (report_2.edit_generation_monthly or 0)
                    existing_combined.edit_pr_monthly = round(((report_1.edit_pr_monthly or 0) + (report_2.edit_pr_monthly or 0)) / 2, 2)
                    existing_combined.edit_poa_monthly = ((report_1.edit_poa_monthly or 0) + (report_2.edit_poa_monthly or 0)) / 2
                    existing_combined.edit_action = True

                # Update wind data if available
                if wind_data:
                    existing_combined.generation_wind = wind_data["generation"] or 0
                    existing_combined.wind_speed = wind_data["wind_speed"] or 0
                    existing_combined.generation_wind_monthly = wind_data["generation_monthly"] or 0
                    existing_combined.wind_speed_monthly = wind_data["wind_speed_monthly"] or 0
                    existing_combined.edit_generation_wind = wind_data["generation"]
                    existing_combined.edit_wind_speed = wind_data["wind_speed"]
                    existing_combined.edit_generation_wind_monthly = wind_data["generation_monthly"]
                    existing_combined.edit_wind_speed_monthly = wind_data["wind_speed_monthly"]
                    existing_combined.csv_report_data = wind_data["csv_report_data"]
                    existing_combined.edit_csv_report_data = wind_data["csv_report_data"]

                # Combine comments if both have them
                comment_text = None
                if getattr(report_1, "comments", None) and getattr(report_2, "comments", None):
                    comment_text = f"{report_1.comments}; {report_2.comments}"
                elif getattr(report_1, "comments", None):
                    comment_text = report_1.comments
                elif getattr(report_2, "comments", None):
                    comment_text = report_2.comments

                if comment_text:
                    existing_combined.comments = comment_text

                existing_combined.status = "Updated"

                # Regenerate PDF
                pdf_dir = "static/both_final_report"
                os.makedirs(pdf_dir, exist_ok=True)
                pdf_path = os.path.join(pdf_dir, f"{combined_plant_id}_{wind_plant_id}_DGR_{date_obj}.jpg")
                existing_combined.dgr_path = pdf_path
                if pdf_path and os.path.exists(pdf_path):
                    try:
                        os.remove(pdf_path)
                        logger.info(f"Old PDF removed: {pdf_path}")
                    except Exception as e:
                        logger.warning(f"Could not remove old PDF: {e}")

                # Generate new PDF
                try:
                    from helper.utils import generate_combined_both_pdf, get_capacity_from_csv

                    # Create temp CSV file for PDF generation
                    static_csv_dir = os.path.join("static", "temp_csv")
                    os.makedirs(static_csv_dir, exist_ok=True)
                    csv_filename = f"jodhani_update_{existing_combined.id}.csv"
                    csv_path = os.path.join(static_csv_dir, csv_filename)

                    if wind_turbine_json:
                        with open(csv_path, "w", encoding="utf-8", newline='') as f:
                            writer = csv.DictWriter(f, fieldnames=["Loc No", "Avg Wind Speed", "Daily Generation (KWh)"])
                            writer.writeheader()
                            for row in wind_turbine_json:
                                writer.writerow({
                                    "Loc No": row.get("Loc No", ""),
                                    "Avg Wind Speed": row.get("Avg Wind Speed", ""),
                                    "Daily Generation (KWh)": row.get("Daily Generation (KWh)", "")
                                })
                        solar_capacity = int(get_capacity_from_csv(plant_id_1) or 0.0) + int(get_capacity_from_csv(plant_id_2) or 0.0)
                        generate_combined_both_pdf(
                            date=str(date_obj),
                            customer_name=combined_plant_name,
                            project="INTEGRUM",
                            daily_poa=existing_combined.edit_poa or existing_combined.poa or 0.0,
                            daily_pr=existing_combined.edit_pr or existing_combined.pr or 0.0,
                            daily_gen_solar=existing_combined.edit_generation_solar or existing_combined.generation_solar or 0.0,
                            month_gen_solar=existing_combined.edit_generation_solar_monthly or existing_combined.generation_solar_monthly or 0.0,
                            month_pr_solar=existing_combined.edit_pr_monthly or existing_combined.pr_monthly or 0.0,
                            monthly_poa_solar=existing_combined.edit_poa_monthly or existing_combined.poa_monthly or 0.0,
                            daily_wind_speed=existing_combined.edit_wind_speed or existing_combined.wind_speed or 0.0,
                            daily_gen_wind=existing_combined.edit_generation_wind or existing_combined.generation_wind or 0.0,
                            monthly_wind=existing_combined.edit_wind_speed_monthly or existing_combined.wind_speed_monthly or 0.0,
                            monthly_gen_wind=existing_combined.edit_generation_wind_monthly or existing_combined.generation_wind_monthly or 0.0,
                            wind_capacity=get_capacity_from_csv(wind_plant_id),
                            solar_capacity=str(solar_capacity),
                            csv_filename=csv_path,
                            plant_name_solar=combined_plant_id,
                            plant_name_wind=wind_plant_id,
                            output_file=pdf_path,
                            comment_text=existing_combined.comments
                        )

                        # Clean up temp CSV
                        try:
                            os.remove(csv_path)
                        except Exception as e:
                            logger.warning(f"Could not remove temp CSV file: {e}")

                        logger.info(f"Updated existing combined Jodhani both report for {date_obj}")

                except Exception as e:
                    logger.exception(f"Error regenerating PDF for updated Jodhani both report: {e}")

                # Commit the database changes regardless of PDF generation success/failure
                s.commit()
                logger.info(f"Database changes committed for updated Jodhani both report for {date_obj}")

                return {
                    "status": "updated",
                    "paired": True,
                    "message": f"Combined both report updated for {date_obj}"
                }
            else:
                logger.info(f"Combined Jodhani both report already exists and is sent for {date_obj}")
                return {
                    "status": "sent",
                    "paired": False,
                    "message": f"Combined both report already exists and is sent for {date_obj}"
                }

        # Find individual solar reports for both plants
        # JOD1 is not hybrid (SolarReport table), JOD2 is hybrid (DgrBothDb table)
        report_1 = s.query(SolarReport).filter_by(
            date=date_obj,
            plant_short_name=plant_id_1
        ).first()

        # JOD2 might be in DgrBothDb as individual hybrid record
        report_2 = s.query(SolarReport).filter_by(
            date=date_obj,
            plant_short_name=plant_id_2
        ).first()

        # If not found in SolarReport, check DgrBothDb for JOD2
        if not report_2:
            both_record_2 = s.query(DgrBothDb).filter_by(
                date=date_obj,
                plant_short_name_solar=plant_id_2
            ).first()
            if both_record_2:
                # Create a mock SolarReport-like object from DgrBothDb data
                class MockSolarReport:
                    def __init__(self, both_record):
                        self.generation = both_record.generation_solar
                        self.pr = both_record.pr
                        self.poa = both_record.poa
                        self.generation_monthly = both_record.generation_solar_monthly
                        self.pr_monthly = both_record.pr_monthly
                        self.poa_monthly = both_record.poa_monthly
                        self.edit_generation = both_record.edit_generation_solar
                        self.edit_pr = both_record.edit_pr
                        self.edit_poa = both_record.edit_poa
                        self.edit_generation_monthly = both_record.edit_generation_solar_monthly
                        self.edit_pr_monthly = both_record.edit_pr_monthly
                        self.edit_poa_monthly = both_record.edit_poa_monthly
                        self.comments = both_record.comments

                report_2 = MockSolarReport(both_record_2)

        if not report_1 or not report_2:
            logger.warning(f"Cannot create combined Jodhani both report for {date_obj}: missing individual solar report(s)")
            return {
                "status": "error",
                "paired": False,
                "message": f"Cannot create combined report for {date_obj}: missing individual solar report(s)"
            }

        # Get wind data from DataFrame (following existing both table approach)
        wind_data = None
        wind_turbine_json = None
        if wind_df is not None and not wind_df.empty:
            # Aggregate wind data for the specific plant and date
            wind_filtered = wind_df[
                (wind_df["Plant ID"] == wind_plant_id) &
                (pd.to_datetime(wind_df["Date"]).dt.date == date_obj) &
                (wind_df["Edit Action"] == "Updated")
            ]
            if not wind_filtered.empty:
                # Turbine-wise JSON
                wind_turbine_json = [
                    {
                        "Loc No": row.get("Turbine Name", ""),
                        "Avg Wind Speed": row.get("Edit AVG. Wind Speed", None),
                        "Daily Generation (KWh)": row.get("Edit Generation", None)
                    }
                    for _, row in wind_filtered.iterrows()
                ]
                wind_data = {
                    "generation": wind_filtered["Edit Generation"].sum(),
                    "wind_speed": wind_filtered["Edit AVG. Wind Speed"].mean(),
                    "generation_monthly": wind_filtered["Edit Generation Monthly"].sum(),
                    "wind_speed_monthly": wind_filtered["Edit AVG. Wind Speed Monthly"].mean(),
                    "csv_report_data": json.dumps(wind_turbine_json, ensure_ascii=False)
                }

        if not wind_data:
            logger.warning(f"Cannot create combined Jodhani both report for {date_obj}: missing wind data for {wind_plant_id}")
            return {
                "status": "error",
                "paired": False,
                "message": f"Cannot create combined report for {date_obj}: missing wind data"
            }

        # Combine the data from both solar reports
        # Sum generation values, but AVERAGE PR and POA values (they are ratios/percentages)
        combined_solar_data = {
            "generation_solar": (report_1.generation or 0) + (report_2.generation or 0),
            "pr": round(((report_1.pr or 0) + (report_2.pr or 0)) / 2, 2),
            "poa": ((report_1.poa or 0) + (report_2.poa or 0)) / 2,
            "generation_solar_monthly": (report_1.generation_monthly or 0) + (report_2.generation_monthly or 0),
            "pr_monthly": round(((report_1.pr_monthly or 0) + (report_2.pr_monthly or 0)) / 2, 2),
            "poa_monthly": ((report_1.poa_monthly or 0) + (report_2.poa_monthly or 0)) / 2,

            # Initialize edit fields
            "edit_generation_solar": None,
            "edit_pr": None,
            "edit_poa": None,
            "edit_generation_solar_monthly": None,
            "edit_pr_monthly": None,
            "edit_poa_monthly": None,
        }

        # Handle edit fields if both reports have them (following Stovekraft approach)
        if (hasattr(report_1, 'edit_generation') and report_1.edit_generation is not None and
            hasattr(report_2, 'edit_generation') and report_2.edit_generation is not None):
            combined_solar_data.update({
                "edit_generation_solar": (report_1.edit_generation or 0) + (report_2.edit_generation or 0),
                "edit_pr": round((report_1.edit_pr or 0) + (report_2.edit_pr or 0), 2),
                "edit_poa": (report_1.edit_poa or 0) + (report_2.edit_poa or 0),
                "edit_generation_solar_monthly": (report_1.edit_generation_monthly or 0) + (report_2.edit_generation_monthly or 0),
                "edit_pr_monthly": round((report_1.edit_pr_monthly or 0) + (report_2.edit_pr_monthly or 0), 2),
                "edit_poa_monthly": (report_1.edit_poa_monthly or 0) + (report_2.edit_poa_monthly or 0),
            })

        # Combine comments if both have them
        comment_text = None
        if getattr(report_1, "comments", None) and getattr(report_2, "comments", None):
            comment_text = f"{report_1.comments}; {report_2.comments}"
        elif getattr(report_1, "comments", None):
            comment_text = report_1.comments
        elif getattr(report_2, "comments", None):
            comment_text = report_2.comments

        # Create new combined both report
        pdf_dir = "static/both_final_report"
        os.makedirs(pdf_dir, exist_ok=True)
        pdf_path = os.path.join(pdf_dir, f"{combined_plant_id}_{wind_plant_id}_DGR_{date_obj}.jpg")

        new_both_report = DgrBothDb(
            date=date_obj,
            plant_short_name_solar=combined_plant_id,
            plant_long_name_solar=combined_plant_name,
            generation_solar=combined_solar_data["generation_solar"],
            pr=combined_solar_data["pr"],
            poa=combined_solar_data["poa"],
            generation_solar_monthly=combined_solar_data["generation_solar_monthly"],
            pr_monthly=combined_solar_data["pr_monthly"],
            poa_monthly=combined_solar_data["poa_monthly"],
            edit_generation_solar=combined_solar_data["edit_generation_solar"],
            edit_pr=combined_solar_data["edit_pr"],
            edit_poa=combined_solar_data["edit_poa"],
            edit_generation_solar_monthly=combined_solar_data["edit_generation_solar_monthly"],
            edit_pr_monthly=combined_solar_data["edit_pr_monthly"],
            edit_poa_monthly=combined_solar_data["edit_poa_monthly"],
            plant_short_name_wind=wind_plant_id,
            plant_long_name_wind=wind_plant_name,
            generation_wind=wind_data["generation"] or 0,
            wind_speed=wind_data["wind_speed"] or 0,
            generation_wind_monthly=wind_data["generation_monthly"] or 0,
            wind_speed_monthly=wind_data["wind_speed_monthly"] or 0,
            edit_generation_wind=wind_data["generation"],
            edit_wind_speed=wind_data["wind_speed"],
            edit_generation_wind_monthly=wind_data["generation_monthly"],
            edit_wind_speed_monthly=wind_data["wind_speed_monthly"],
            csv_report_data=wind_data["csv_report_data"],
            edit_csv_report_data=wind_data["csv_report_data"],
            dgr_path=pdf_path,
            status="Updated",
            comments=comment_text,
        )

        s.add(new_both_report)
        s.flush()  # Get the ID

        # Generate PDF for the combined both report (following Stovekraft approach)
        try:
            from helper.utils import generate_combined_both_pdf, get_capacity_from_csv

            # Get combined capacity for solar plants
            solar_capacity = int(get_capacity_from_csv(plant_id_1) or 0.0) + int(get_capacity_from_csv(plant_id_2) or 0.0)
            wind_capacity = get_capacity_from_csv(wind_plant_id) or 0.0

            generate_combined_both_pdf(
                date=str(date_obj),
                customer_name=combined_plant_name,
                project="INTEGRUM",
                daily_poa=new_both_report.poa or 0.0,
                daily_pr=new_both_report.pr or 0.0,
                daily_gen_solar=new_both_report.generation_solar or 0.0,
                month_gen_solar=new_both_report.generation_solar_monthly or 0.0,
                month_pr_solar=new_both_report.pr_monthly or 0.0,
                monthly_poa_solar=new_both_report.poa_monthly or 0.0,
                daily_wind_speed=new_both_report.wind_speed or 0.0,
                daily_gen_wind=new_both_report.generation_wind or 0.0,
                monthly_wind=new_both_report.wind_speed_monthly or 0.0,
                monthly_gen_wind=new_both_report.generation_wind_monthly or 0.0,
                wind_capacity=wind_capacity,
                solar_capacity=solar_capacity,
                csv_filename=None,
                plant_name_solar=combined_plant_name,
                plant_name_wind=wind_plant_name,
                output_file=pdf_path,
                comment_text=comment_text
            )
            logger.info(f"Generated combined Jodhani both PDF at {pdf_path}")
        except Exception as pdf_error:
            logger.warning(f"Failed to generate PDF for combined Jodhani both report: {pdf_error}")
            # Continue without failing the entire operation

        s.commit()
        logger.info(f"Created combined Jodhani both report for {date_obj}")
        if created:
            s.close()
        return {"status": "success", "paired": True, "message": f"Combined both report created for {date_obj}"}

    except Exception as e:
        if created:
            s.rollback()
            s.close()
        logger.exception(f"Error pairing Jodhani both reports for {date_obj}")
        return {"status": "error", "paired": False, "message": f"Error pairing reports: {str(e)}"}

# --- Balaji Both Pairing Function ---
def pair_balaji_both_reports(date_obj: datetime.date, wind_df=None, db_session=None):
    """
    Pair solar reports for Balaji plants (IN.INTE.BAL1 & IN.INTE.BAL2)
    into a single combined report in the both table (DgrBothDb).

    This function:
    1. Finds existing separate solar reports for both plant IDs on the given date
    2. Combines their data (sums generation values, averages PR/POA values)
    3. Creates a new combined report in DgrBothDb with plant_short_name_solar as "IN.INTE.BAL1&IN.INTE.BAL2"
    4. Updates plant_long_name_solar to "Balaji Malts 1&2 Private Limited"
    5. Keeps the original individual reports (does NOT delete them)

    Args:
        date_obj (datetime.date): The date for which to pair the reports
        db_session: Optional SQLAlchemy session

    Returns:
        dict: {"status": "success", "paired": True/False, "message": str}
    """
    s, created = _get_session(db_session)

    try:
        plant_id_1 = "IN.INTE.BAL1"
        plant_id_2 = "IN.INTE.BAL2"
        combined_plant_id = f"{plant_id_1}&{plant_id_2}"
        combined_plant_name = "Balaji Malts 1&2 Private Limited"
        wind_plant_id = "IN.INTE.BMPL"  # From CSV: Balaji wind plant
        wind_plant_name = "Balaji Malts Private Limited wind"

        # Check if combined report already exists in both table
        existing_combined = s.query(DgrBothDb).filter_by(
            date=date_obj,
            plant_short_name_solar=combined_plant_id,
            plant_short_name_wind=wind_plant_id
        ).first()

        if existing_combined:
            # If the report exists and is not sent, update it
            if getattr(existing_combined, "status", None) != "Sent":
                # Check if report is approved - if approved, skip update
                if getattr(existing_combined, "approved", False):
                    logger.info(f"Combined Balaji both report for {date_obj} is approved, skipping update")
                    return {
                        "status": "approved",
                        "paired": False,
                        "message": f"Combined both report for {date_obj} is approved and cannot be updated"
                    }

                # Find individual solar reports for both plants to update the combined report
                report_1 = s.query(SolarReport).filter_by(
                    date=date_obj,
                    plant_short_name=plant_id_1
                ).first()

                # If not found in SolarReport, check DgrBothDb for BAL1
                if not report_1:
                    both_record_1 = s.query(DgrBothDb).filter_by(
                        date=date_obj,
                        plant_short_name_solar=plant_id_1
                    ).first()
                    if both_record_1:
                        # Create a mock SolarReport-like object from DgrBothDb data
                        class MockSolarReport:
                            def __init__(self, both_record):
                                self.generation = both_record.generation_solar
                                self.pr = both_record.pr
                                self.poa = both_record.poa
                                self.generation_monthly = both_record.generation_solar_monthly
                                self.pr_monthly = both_record.pr_monthly
                                self.poa_monthly = both_record.poa_monthly
                                self.edit_generation = both_record.edit_generation_solar
                                self.edit_pr = both_record.edit_pr
                                self.edit_poa = both_record.edit_poa
                                self.edit_generation_monthly = both_record.edit_generation_solar_monthly
                                self.edit_pr_monthly = both_record.edit_pr_monthly
                                self.edit_poa_monthly = both_record.edit_poa_monthly
                                self.comments = both_record.comments

                        report_1 = MockSolarReport(both_record_1)

                report_2 = s.query(SolarReport).filter_by(
                    date=date_obj,
                    plant_short_name=plant_id_2
                ).first()

                if not report_1 or not report_2:
                    logger.warning(f"Cannot update combined Balaji both report for {date_obj}: missing individual solar report(s)")
                    return {
                        "status": "error",
                        "paired": False,
                        "message": f"Cannot update combined report for {date_obj}: missing individual solar report(s)"
                    }

                # Get wind data from DataFrame
                wind_data = None
                wind_turbine_json = None
                if wind_df is not None and not wind_df.empty:
                    # Aggregate wind data for the specific plant and date
                    wind_filtered = wind_df[
                        (wind_df["Plant ID"] == wind_plant_id) &
                        (pd.to_datetime(wind_df["Date"]).dt.date == date_obj) &
                        (wind_df["Edit Action"] == "Updated")
                    ]
                    if not wind_filtered.empty:
                        # Turbine-wise JSON
                        wind_turbine_json = [
                            {
                                "Loc No": row.get("Turbine Name", ""),
                                "Avg Wind Speed": row.get("Edit AVG. Wind Speed", None),
                                "Daily Generation (KWh)": row.get("Edit Generation", None)
                            }
                            for _, row in wind_filtered.iterrows()
                        ]
                        wind_data = {
                            "generation": wind_filtered["Edit Generation"].sum(),
                            "wind_speed": wind_filtered["Edit AVG. Wind Speed"].mean(),
                            "generation_monthly": wind_filtered["Edit Generation Monthly"].sum(),
                            "wind_speed_monthly": wind_filtered["Edit AVG. Wind Speed Monthly"].mean(),
                            "csv_report_data": json.dumps(wind_turbine_json, ensure_ascii=False)
                        }

                # Update existing combined report with new data
                # Combine the data from both solar reports (sum generation, average PR/POA)
                existing_combined.generation_solar = (report_1.generation or 0) + (report_2.generation or 0)
                existing_combined.pr = round(((report_1.pr or 0) + (report_2.pr or 0)) / 2, 2)
                existing_combined.poa = ((report_1.poa or 0) + (report_2.poa or 0)) / 2
                existing_combined.generation_solar_monthly = (report_1.generation_monthly or 0) + (report_2.generation_monthly or 0)
                existing_combined.pr_monthly = round(((report_1.pr_monthly or 0) + (report_2.pr_monthly or 0)) / 2, 2)
                existing_combined.poa_monthly = ((report_1.poa_monthly or 0) + (report_2.poa_monthly or 0)) / 2

                # Handle edit fields if both reports have them
                if (hasattr(report_1, 'edit_generation') and report_1.edit_generation is not None and
                    hasattr(report_2, 'edit_generation') and report_2.edit_generation is not None):
                    existing_combined.edit_generation_solar = (report_1.edit_generation or 0) + (report_2.edit_generation or 0)
                    existing_combined.edit_pr = round(((report_1.edit_pr or 0) + (report_2.edit_pr or 0)) / 2, 2)
                    existing_combined.edit_poa = ((report_1.edit_poa or 0) + (report_2.edit_poa or 0)) / 2
                    existing_combined.edit_generation_solar_monthly = (report_1.edit_generation_monthly or 0) + (report_2.edit_generation_monthly or 0)
                    existing_combined.edit_pr_monthly = round(((report_1.edit_pr_monthly or 0) + (report_2.edit_pr_monthly or 0)) / 2, 2)
                    existing_combined.edit_poa_monthly = ((report_1.edit_poa_monthly or 0) + (report_2.edit_poa_monthly or 0)) / 2
                    existing_combined.edit_action = True

                # Update wind data if available
                if wind_data:
                    existing_combined.generation_wind = wind_data["generation"] or 0
                    existing_combined.wind_speed = wind_data["wind_speed"] or 0
                    existing_combined.generation_wind_monthly = wind_data["generation_monthly"] or 0
                    existing_combined.wind_speed_monthly = wind_data["wind_speed_monthly"] or 0
                    existing_combined.edit_generation_wind = wind_data["generation"]
                    existing_combined.edit_wind_speed = wind_data["wind_speed"]
                    existing_combined.edit_generation_wind_monthly = wind_data["generation_monthly"]
                    existing_combined.edit_wind_speed_monthly = wind_data["wind_speed_monthly"]
                    existing_combined.csv_report_data = wind_data["csv_report_data"]
                    existing_combined.edit_csv_report_data = wind_data["csv_report_data"]

                # Combine comments if both have them
                comment_text = None
                if getattr(report_1, "comments", None) and getattr(report_2, "comments", None):
                    comment_text = f"{report_1.comments}; {report_2.comments}"
                elif getattr(report_1, "comments", None):
                    comment_text = report_1.comments
                elif getattr(report_2, "comments", None):
                    comment_text = report_2.comments

                if comment_text:
                    existing_combined.comments = comment_text

                existing_combined.status = "Updated"

                # Regenerate PDF
                pdf_dir = "static/both_final_report"
                os.makedirs(pdf_dir, exist_ok=True)
                pdf_path = os.path.join(pdf_dir, f"{combined_plant_id}_{wind_plant_id}_DGR_{date_obj}.jpg")
                existing_combined.dgr_path = pdf_path
                if pdf_path and os.path.exists(pdf_path):
                    try:
                        os.remove(pdf_path)
                        logger.info(f"Old PDF removed: {pdf_path}")
                    except Exception as e:
                        logger.warning(f"Could not remove old PDF: {e}")

                # Generate new PDF
                try:
                    from helper.utils import generate_combined_both_pdf, get_capacity_from_csv

                    # Create temp CSV file for PDF generation
                    static_csv_dir = os.path.join("static", "temp_csv")
                    os.makedirs(static_csv_dir, exist_ok=True)
                    csv_filename = f"balaji_update_{existing_combined.id}.csv"
                    csv_path = os.path.join(static_csv_dir, csv_filename)

                    if wind_turbine_json:
                        with open(csv_path, "w", encoding="utf-8", newline='') as f:
                            writer = csv.DictWriter(f, fieldnames=["Loc No", "Avg Wind Speed", "Daily Generation (KWh)"])
                            writer.writeheader()
                            for row in wind_turbine_json:
                                writer.writerow({
                                    "Loc No": row.get("Loc No", ""),
                                    "Avg Wind Speed": row.get("Avg Wind Speed", ""),
                                    "Daily Generation (KWh)": row.get("Daily Generation (KWh)", "")
                                })
                        solar_capacity = int(get_capacity_from_csv(plant_id_1) or 0.0) + int(get_capacity_from_csv(plant_id_2) or 0.0)
                        
                        generate_combined_both_pdf(
                            date=str(date_obj),
                            customer_name=combined_plant_name,
                            project="INTEGRUM",
                            daily_poa=existing_combined.edit_poa or existing_combined.poa or 0.0,
                            daily_pr=existing_combined.edit_pr or existing_combined.pr or 0.0,
                            daily_gen_solar=existing_combined.edit_generation_solar or existing_combined.generation_solar or 0.0,
                            month_gen_solar=existing_combined.edit_generation_solar_monthly or existing_combined.generation_solar_monthly or 0.0,
                            month_pr_solar=existing_combined.edit_pr_monthly or existing_combined.pr_monthly or 0.0,
                            monthly_poa_solar=existing_combined.edit_poa_monthly or existing_combined.poa_monthly or 0.0,
                            daily_wind_speed=existing_combined.edit_wind_speed or existing_combined.wind_speed or 0.0,
                            daily_gen_wind=existing_combined.edit_generation_wind or existing_combined.generation_wind or 0.0,
                            monthly_wind=existing_combined.edit_wind_speed_monthly or existing_combined.wind_speed_monthly or 0.0,
                            monthly_gen_wind=existing_combined.edit_generation_wind_monthly or existing_combined.generation_wind_monthly or 0.0,
                            wind_capacity=get_capacity_from_csv(wind_plant_id),
                            solar_capacity=str(solar_capacity),
                            csv_filename=csv_path,
                            plant_name_solar=combined_plant_id,
                            plant_name_wind=wind_plant_id,
                            output_file=pdf_path,
                            comment_text=existing_combined.comments
                        )

                        # Clean up temp CSV
                        try:
                            os.remove(csv_path)
                        except Exception as e:
                            logger.warning(f"Could not remove temp CSV file: {e}")

                        logger.info(f"Updated existing combined Balaji both report for {date_obj}")

                except Exception as e:
                    logger.exception(f"Error regenerating PDF for updated Balaji both report: {e}")

                # Commit the database changes regardless of PDF generation success/failure
                s.commit()
                logger.info(f"Database changes committed for updated Balaji both report for {date_obj}")

                return {
                    "status": "updated",
                    "paired": True,
                    "message": f"Combined both report updated for {date_obj}"
                }
            else:
                logger.info(f"Combined Balaji both report already exists and is sent for {date_obj}")
                return {
                    "status": "sent",
                    "paired": False,
                    "message": f"Combined both report already exists and is sent for {date_obj}"
                }

        # Find individual solar reports for both plants
        # BAL1 is hybrid (DgrBothDb table), BAL2 is not hybrid (SolarReport table)
        report_1 = s.query(SolarReport).filter_by(
            date=date_obj,
            plant_short_name=plant_id_1
        ).first()

        # If not found in SolarReport, check DgrBothDb for BAL1
        if not report_1:
            both_record_1 = s.query(DgrBothDb).filter_by(
                date=date_obj,
                plant_short_name_solar=plant_id_1
            ).first()
            if both_record_1:
                # Create a mock SolarReport-like object from DgrBothDb data
                class MockSolarReport:
                    def __init__(self, both_record):
                        self.generation = both_record.generation_solar
                        self.pr = both_record.pr
                        self.poa = both_record.poa
                        self.generation_monthly = both_record.generation_solar_monthly
                        self.pr_monthly = both_record.pr_monthly
                        self.poa_monthly = both_record.poa_monthly
                        self.edit_generation = both_record.edit_generation_solar
                        self.edit_pr = both_record.edit_pr
                        self.edit_poa = both_record.edit_poa
                        self.edit_generation_monthly = both_record.edit_generation_solar_monthly
                        self.edit_pr_monthly = both_record.edit_pr_monthly
                        self.edit_poa_monthly = both_record.edit_poa_monthly
                        self.comments = both_record.comments

                report_1 = MockSolarReport(both_record_1)

        report_2 = s.query(SolarReport).filter_by(
            date=date_obj,
            plant_short_name=plant_id_2
        ).first()

        if not report_1 or not report_2:
            logger.warning(f"Cannot create combined Balaji both report for {date_obj}: missing individual solar report(s)")
            return {
                "status": "error",
                "paired": False,
                "message": f"Cannot create combined report for {date_obj}: missing individual solar report(s)"
            }

        # Get wind data from DataFrame (following existing both table approach)
        wind_data = None
        wind_turbine_json = None
        if wind_df is not None and not wind_df.empty:
            # Aggregate wind data for the specific plant and date
            wind_filtered = wind_df[
                (wind_df["Plant ID"] == wind_plant_id) &
                (pd.to_datetime(wind_df["Date"]).dt.date == date_obj) &
                (wind_df["Edit Action"] == "Updated")
            ]
            if not wind_filtered.empty:
                # Turbine-wise JSON
                wind_turbine_json = [
                    {
                        "Loc No": row.get("Turbine Name", ""),
                        "Avg Wind Speed": row.get("Edit AVG. Wind Speed", None),
                        "Daily Generation (KWh)": row.get("Edit Generation", None)
                    }
                    for _, row in wind_filtered.iterrows()
                ]
                wind_data = {
                    "generation": wind_filtered["Edit Generation"].sum(),
                    "wind_speed": wind_filtered["Edit AVG. Wind Speed"].mean(),
                    "generation_monthly": wind_filtered["Edit Generation Monthly"].sum(),
                    "wind_speed_monthly": wind_filtered["Edit AVG. Wind Speed Monthly"].mean(),
                    "csv_report_data": json.dumps(wind_turbine_json, ensure_ascii=False)
                }

        if not wind_data:
            logger.warning(f"Cannot create combined Balaji both report for {date_obj}: missing wind data for {wind_plant_id}")
            return {
                "status": "error",
                "paired": False,
                "message": f"Cannot create combined report for {date_obj}: missing wind data"
            }

        # Combine the data from both solar reports
        # Sum generation values, but AVERAGE PR and POA values (they are ratios/percentages)
        combined_solar_data = {
            "generation_solar": (report_1.generation or 0) + (report_2.generation or 0),
            "pr": round(((report_1.pr or 0) + (report_2.pr or 0)) / 2, 2),
            "poa": ((report_1.poa or 0) + (report_2.poa or 0)) / 2,
            "generation_solar_monthly": (report_1.generation_monthly or 0) + (report_2.generation_monthly or 0),
            "pr_monthly": round(((report_1.pr_monthly or 0) + (report_2.pr_monthly or 0)) / 2, 2),
            "poa_monthly": ((report_1.poa_monthly or 0) + (report_2.poa_monthly or 0)) / 2,

            # Initialize edit fields
            "edit_generation_solar": None,
            "edit_pr": None,
            "edit_poa": None,
            "edit_generation_solar_monthly": None,
            "edit_pr_monthly": None,
            "edit_poa_monthly": None,
        }

        # Handle edit fields if both reports have them (following Stovekraft approach)
        if (hasattr(report_1, 'edit_generation') and report_1.edit_generation is not None and
            hasattr(report_2, 'edit_generation') and report_2.edit_generation is not None):
            combined_solar_data.update({
                "edit_generation_solar": (report_1.edit_generation or 0) + (report_2.edit_generation or 0),
            "edit_pr": round((report_1.edit_pr or 0) + (report_2.edit_pr or 0), 2),
                "edit_poa": (report_1.edit_poa or 0) + (report_2.edit_poa or 0),
                "edit_generation_solar_monthly": (report_1.edit_generation_monthly or 0) + (report_2.edit_generation_monthly or 0),
            "edit_pr_monthly": round((report_1.edit_pr_monthly or 0) + (report_2.edit_pr_monthly or 0), 2),
                "edit_poa_monthly": (report_1.edit_poa_monthly or 0) + (report_2.edit_poa_monthly or 0),
            })

        # Combine comments if both have them
        comment_text = None
        if getattr(report_1, "comments", None) and getattr(report_2, "comments", None):
            comment_text = f"{report_1.comments}; {report_2.comments}"
        elif getattr(report_1, "comments", None):
            comment_text = report_1.comments
        elif getattr(report_2, "comments", None):
            comment_text = report_2.comments

        # Create new combined both report
        pdf_dir = "static/both_final_report"
        os.makedirs(pdf_dir, exist_ok=True)
        pdf_path = os.path.join(pdf_dir, f"{combined_plant_id}_{wind_plant_id}_DGR_{date_obj}.jpg")

        new_both_report = DgrBothDb(
            date=date_obj,
            plant_short_name_solar=combined_plant_id,
            plant_long_name_solar=combined_plant_name,
            generation_solar=combined_solar_data["generation_solar"],
            pr=combined_solar_data["pr"],
            poa=combined_solar_data["poa"],
            generation_solar_monthly=combined_solar_data["generation_solar_monthly"],
            pr_monthly=combined_solar_data["pr_monthly"],
            poa_monthly=combined_solar_data["poa_monthly"],
            edit_generation_solar=combined_solar_data["edit_generation_solar"],
            edit_pr=combined_solar_data["edit_pr"],
            edit_poa=combined_solar_data["edit_poa"],
            edit_generation_solar_monthly=combined_solar_data["edit_generation_solar_monthly"],
            edit_pr_monthly=combined_solar_data["edit_pr_monthly"],
            edit_poa_monthly=combined_solar_data["edit_poa_monthly"],
            plant_short_name_wind=wind_plant_id,
            plant_long_name_wind=wind_plant_name,
            generation_wind=wind_data["generation"] or 0,
            wind_speed=wind_data["wind_speed"] or 0,
            generation_wind_monthly=wind_data["generation_monthly"] or 0,
            wind_speed_monthly=wind_data["wind_speed_monthly"] or 0,
            edit_generation_wind=wind_data["generation"],
            edit_wind_speed=wind_data["wind_speed"],
            edit_generation_wind_monthly=wind_data["generation_monthly"],
            edit_wind_speed_monthly=wind_data["wind_speed_monthly"],
            csv_report_data=wind_data["csv_report_data"],
            edit_csv_report_data=wind_data["csv_report_data"],
            dgr_path=pdf_path,
            status="Updated",
            comments=comment_text,
        )

        s.add(new_both_report)
        s.flush()  # Get the ID

        # Generate PDF for the combined both report (following Stovekraft approach)
        try:
            from helper.utils import generate_combined_both_pdf, get_capacity_from_csv

            # Get combined capacity for solar plants
            solar_capacity = (get_capacity_from_csv(plant_id_1) or 0.0) + (get_capacity_from_csv(plant_id_2) or 0.0)
            wind_capacity = get_capacity_from_csv(wind_plant_id) or 0.0

            generate_combined_both_pdf(
                date=str(date_obj),
                customer_name=combined_plant_name,
                project="INTEGRUM",
                daily_poa=new_both_report.poa or 0.0,
                daily_pr=new_both_report.pr or 0.0,
                daily_gen_solar=new_both_report.generation_solar or 0.0,
                month_gen_solar=new_both_report.generation_solar_monthly or 0.0,
                month_pr_solar=new_both_report.pr_monthly or 0.0,
                monthly_poa_solar=new_both_report.poa_monthly or 0.0,
                daily_wind_speed=new_both_report.wind_speed or 0.0,
                daily_gen_wind=new_both_report.generation_wind or 0.0,
                monthly_wind=new_both_report.wind_speed_monthly or 0.0,
                monthly_gen_wind=new_both_report.generation_wind_monthly or 0.0,
                wind_capacity=wind_capacity,
                solar_capacity=solar_capacity,
                csv_filename=None,
                plant_name_solar=combined_plant_name,
                plant_name_wind=wind_plant_name,
                output_file=pdf_path,
                comment_text=comment_text
            )
            logger.info(f"Generated combined Balaji both PDF at {pdf_path}")
        except Exception as pdf_error:
            logger.warning(f"Failed to generate PDF for combined Balaji both report: {pdf_error}")
            # Continue without failing the entire operation

        s.commit()
        logger.info(f"Created combined Balaji both report for {date_obj}")
        if created:
            s.close()
        return {"status": "success", "paired": True, "message": f"Combined both report created for {date_obj}"}

    except Exception as e:
        if created:
            s.rollback()
            s.close()
        logger.exception(f"Error pairing Balaji both reports for {date_obj}")
        return {"status": "error", "paired": False, "message": f"Error pairing reports: {str(e)}"}




# --- CLI entry ---
if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        logger.error("Usage: python import_turbine_inverter_data.py <excel_file_path>")
    else:
        try:
            total = import_turbine_inverter_edits(sys.argv[1])
            logger.info("Finished. Total turbine/inverter updates: %d", total)
        except Exception:
            logger.exception("Import failed.")
            sys.exit(1)
